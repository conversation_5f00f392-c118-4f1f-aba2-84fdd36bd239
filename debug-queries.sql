-- Debug queries to check the database state

-- 1. Check if user 0001 exists
SELECT code, nickname, "isSuperAdmin", "isActive", "companyCode", "isDeleted" 
FROM users 
WHERE code = '0001';

-- 2. Check all users to see what codes exist
SELECT code, nickname, "isSuperAdmin", "isActive", "companyCode", "isDeleted" 
FROM users 
WHERE "isDeleted" = false
ORDER BY code;

-- 3. Check if company '01' exists
SELECT code, name, "isDeleted" 
FROM companies 
WHERE code = '01';

-- 4. Check all companies
SELECT code, name, "isDeleted" 
FROM companies 
WHERE "isDeleted" = false
ORDER BY code;

-- 5. Check for super admin users
SELECT code, nickname, "isSuperAdmin", "isActive" 
FROM users 
WHERE "isSuperAdmin" = true AND "isDeleted" = false;

-- 6. Check the structure of the users table
\d users;

-- 7. Check the structure of the companies table  
\d companies;
