// Debug script to test user update
const axios = require('axios');

async function testUserUpdate() {
  const baseURL = 'http://localhost:8080';

  try {
    // First, let's try to login to get a valid token
    console.log('1. Attempting to login...');

    // You'll need to replace these with actual credentials
    const loginResponse = await axios.post(`${baseURL}/auth/login`, {
      userCode: 'admin', // Replace with actual admin user code
      password: 'password', // Replace with actual password
    });

    const token = loginResponse.data.data.accessToken;
    console.log('Login successful, token obtained');

    // Check if user 0001 exists
    console.log('2. Checking if user 0001 exists...');
    try {
      const userResponse = await axios.get(`${baseURL}/users/0001`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      console.log('User 0001 exists:', userResponse.data.data);
    } catch (error) {
      console.log(
        'User 0001 does not exist or access denied:',
        error.response?.data,
      );
      return;
    }

    // Try the update
    console.log('3. Attempting to update user 0001...');
    const updateData = {
      nickname: 'hhahaha',
      isActive: true,
      routePermissions: {
        '/product/sku': {
          read: true,
          create: true,
          delete: true,
          export: true,
          import: true,
          update: true,
        },
        '/product/brand': {
          read: true,
          create: true,
          delete: true,
          export: true,
          import: true,
          update: true,
        },
        '/product/color': {
          read: true,
          create: true,
          delete: true,
          export: true,
          import: true,
          update: true,
        },
        '/product/category': {
          read: true,
          create: true,
          delete: true,
          export: true,
          import: true,
          update: true,
        },
        '/product/auxiliary': {
          read: true,
          create: true,
          delete: true,
          export: true,
          import: true,
          update: true,
        },
        '/product/skus-inventory': {
          read: true,
          create: true,
          delete: true,
          export: true,
          import: true,
          update: true,
        },
      },
      companyCode: '01',
    };

    const updateResponse = await axios.patch(
      `${baseURL}/users/0001`,
      updateData,
      {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      },
    );

    console.log('Update successful:', updateResponse.data);
  } catch (error) {
    console.error('Error occurred:');
    console.error('Status:', error.response?.status);
    console.error('Data:', error.response?.data);
    console.error('Message:', error.message);

    if (error.response?.status === 500) {
      console.error(
        'This is a 500 Internal Server Error. Check the server logs for more details.',
      );
    }
  }
}

testUserUpdate();
