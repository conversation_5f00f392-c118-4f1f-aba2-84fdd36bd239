import { ApiProperty } from '@nestjs/swagger';
import {
  IsOptional,
  IsString,
  IsDateString,
  IsNumber,
  Min,
  IsEnum,
} from 'class-validator';
import { Type } from 'class-transformer';

export enum SortBy {
  CREATE_DATE = 'createDate',
  AMOUNT = 'amount',
}

export enum SortOrder {
  ASC = 'ASC',
  DESC = 'DESC',
}

export class QueryIncomeDetailsDto {
  @ApiProperty({
    description: '页码（必填）',
    example: 1,
  })
  @Type(() => Number)
  @IsNumber({}, { message: '页码必须是数字' })
  @Min(0, { message: '页码必须大于等于0' })
  page: number;

  @ApiProperty({
    description: '每页数量（必填）',
    example: 10,
  })
  @Type(() => Number)
  @IsNumber({}, { message: '每页数量必须是数字' })
  @Min(0, { message: '每页数量必须大于等于0' })
  pageSize: number;

  @ApiProperty({
    description: '起始日期',
    example: '2023-01-01T00:00:00.000Z',
    required: false,
  })
  @IsOptional()
  @IsDateString()
  startTime?: string;

  @ApiProperty({
    description: '终止日期',
    example: '2023-12-31T23:59:59.999Z',
    required: false,
  })
  @IsOptional()
  @IsDateString()
  endTime?: string;

  @ApiProperty({
    description: '客户搜索（模糊搜索客户编码和名称）',
    example: 'CUS001',
    required: false,
  })
  @IsOptional()
  @IsString()
  customerSearch?: string;

  @ApiProperty({
    description: '负责人搜索（模糊搜索负责人编码和姓名）',
    example: 'user001',
    required: false,
  })
  @IsOptional()
  @IsString()
  responsibleUserSearch?: string;

  @ApiProperty({
    description: '备注搜索',
    example: '销售收入',
    required: false,
  })
  @IsOptional()
  @IsString()
  remarkSearch?: string;

  @ApiProperty({
    description: '公司编码搜索（根据负责人的公司编码进行检索）',
    example: '01',
    required: false,
  })
  @IsOptional()
  @IsString()
  companyCode?: string;

  @ApiProperty({
    description: '排序字段',
    enum: SortBy,
    example: SortBy.CREATE_DATE,
    required: false,
  })
  @IsOptional()
  @IsEnum(SortBy, { message: '排序字段无效' })
  sortBy?: SortBy = SortBy.CREATE_DATE;

  @ApiProperty({
    description: '排序方向',
    enum: SortOrder,
    example: SortOrder.DESC,
    required: false,
  })
  @IsOptional()
  @IsEnum(SortOrder, { message: '排序方向无效' })
  sortOrder?: SortOrder = SortOrder.DESC;
}
