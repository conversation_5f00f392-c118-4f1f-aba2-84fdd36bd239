import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  Logger,
  HttpStatus,
  Res,
  Req,
} from '@nestjs/common';
import { Response } from 'express';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { IncomesService } from './incomes.service';
import { CreateIncomeDetailDto } from './dto/create-income-detail.dto';
import { UpdateIncomeDetailDto } from './dto/update-income-detail.dto';
import { QueryIncomeDetailsDto } from './dto/query-income-details.dto';

@ApiTags('incomes')
@Controller('incomes')
export class IncomesController {
  private readonly logger = new Logger(IncomesController.name);

  constructor(private readonly incomesService: IncomesService) {}

  @Post('details')
  @ApiOperation({ summary: '新增收入明细' })
  @ApiResponse({
    status: 200,
    description: '收入明细创建成功',
  })
  @ApiResponse({
    status: 400,
    description: '请求参数错误或客户/负责人不存在',
  })
  async createDetail(@Body() createDetailDto: CreateIncomeDetailDto) {
    this.logger.log(
      `Creating income detail for customer: ${createDetailDto.customerCode}, amount: ${createDetailDto.amount}`,
    );

    await this.incomesService.createDetail(createDetailDto);

    return {
      code: 200,
      data: null,
      message: '收入明细创建成功',
    };
  }

  @Get('details')
  @ApiOperation({ summary: '分页查询收入明细列表' })
  @ApiQuery({
    name: 'page',
    description: '页码（必填）',
    example: 1,
  })
  @ApiQuery({
    name: 'pageSize',
    description: '每页数量（必填）',
    example: 10,
  })
  @ApiQuery({
    name: 'startTime',
    description: '起始时间',
    example: '2023-01-01T00:00:00.000Z',
    required: false,
  })
  @ApiQuery({
    name: 'endTime',
    description: '结束时间',
    example: '2023-12-31T23:59:59.999Z',
    required: false,
  })
  @ApiQuery({
    name: 'customerSearch',
    description: '客户搜索（模糊搜索客户编码和名称）',
    example: 'CUS001',
    required: false,
  })
  @ApiQuery({
    name: 'responsibleUserSearch',
    description: '负责人搜索（模糊搜索负责人编码和姓名）',
    example: 'user001',
    required: false,
  })
  @ApiQuery({
    name: 'remarkSearch',
    description: '备注搜索',
    example: '销售收入',
    required: false,
  })
  @ApiQuery({
    name: 'companyCode',
    description: '公司编码搜索（根据负责人的公司编码进行检索）',
    example: '01',
    required: false,
  })
  @ApiQuery({
    name: 'sortBy',
    description: '排序字段',
    enum: ['createDate', 'amount'],
    example: 'createDate',
    required: false,
  })
  @ApiQuery({
    name: 'sortOrder',
    description: '排序方向',
    enum: ['ASC', 'DESC'],
    example: 'DESC',
    required: false,
  })
  @ApiResponse({
    status: 200,
    description: '查询成功',
  })
  async findAllDetails(@Query() queryDto: QueryIncomeDetailsDto) {
    this.logger.log(
      `Querying income details with params: ${JSON.stringify(queryDto)}`,
    );

    const result = await this.incomesService.findAllDetails(queryDto);

    return {
      code: 200,
      data: result,
      message: '查询成功',
    };
  }

  @Get('details/:id')
  @ApiOperation({ summary: '获取收入明细详情' })
  @ApiParam({
    name: 'id',
    description: '收入明细ID',
    example: 'uuid',
  })
  @ApiResponse({
    status: 200,
    description: '查询成功',
  })
  @ApiResponse({
    status: 404,
    description: '收入明细不存在',
  })
  async findDetailById(@Param('id') id: string) {
    this.logger.log(`Finding income detail by id: ${id}`);

    const detail = await this.incomesService.findDetailById(id);

    return {
      code: 200,
      data: detail,
      message: '查询成功',
    };
  }

  @Patch('details/:id')
  @ApiOperation({ summary: '更新收入明细' })
  @ApiParam({
    name: 'id',
    description: '收入明细ID',
    example: 'uuid',
  })
  @ApiResponse({
    status: 200,
    description: '更新成功',
  })
  @ApiResponse({
    status: 404,
    description: '收入明细不存在',
  })
  async updateDetail(
    @Param('id') id: string,
    @Body() updateDetailDto: UpdateIncomeDetailDto,
  ) {
    this.logger.log(`Updating income detail ${id}`);

    await this.incomesService.updateDetail(id, updateDetailDto);

    return {
      code: 200,
      data: null,
      message: '收入明细更新成功',
    };
  }

  @Delete('details/:id')
  @ApiOperation({ summary: '删除收入明细' })
  @ApiParam({
    name: 'id',
    description: '收入明细ID',
    example: 'uuid',
  })
  @ApiResponse({
    status: 200,
    description: '删除成功',
  })
  @ApiResponse({
    status: 404,
    description: '收入明细不存在',
  })
  async removeDetail(@Param('id') id: string) {
    this.logger.log(`Removing income detail ${id}`);

    await this.incomesService.removeDetail(id);

    return {
      code: 200,
      data: null,
      message: '收入明细删除成功',
    };
  }

  @Get('export')
  @ApiOperation({ summary: '导出收入明细Excel' })
  @ApiQuery({
    name: 'startTime',
    description: '起始时间',
    example: '2023-01-01',
    required: false,
  })
  @ApiQuery({
    name: 'endTime',
    description: '结束时间',
    example: '2023-12-31',
    required: false,
  })
  @ApiQuery({
    name: 'detailIds',
    description:
      '明细ID列表（逗号分隔，可选）。如果不提供，则必须提供起始时间和终止时间进行时间范围导出',
    example: 'uuid1,uuid2,uuid3',
    required: false,
  })
  @ApiQuery({
    name: 'customerSearch',
    description: '客户搜索',
    example: 'CUS001',
    required: false,
  })
  @ApiQuery({
    name: 'responsibleUserSearch',
    description: '负责人搜索',
    example: 'user001',
    required: false,
  })
  @ApiQuery({
    name: 'companyCode',
    description: '公司编码搜索（根据负责人的公司编码进行检索）',
    example: '01',
    required: false,
  })
  @ApiResponse({
    status: 200,
    description: 'Excel文件导出成功',
  })
  @ApiResponse({
    status: 400,
    description: '请选择明细进行导出，或提供起始时间和终止时间进行时间范围导出',
  })
  async exportDetailsToExcel(@Req() req: any, @Res() res: Response) {
    this.logger.log(`Exporting income details to Excel`);

    try {
      // 从原始请求中获取查询参数
      const {
        startTime,
        endTime,
        detailIds,
        customerSearch,
        responsibleUserSearch,
        companyCode,
      } = req.query;

      // 验证：如果没有提供明细ID，必须提供时间范围
      if (!detailIds || detailIds.trim() === '') {
        if (!startTime || !endTime) {
          this.logger.warn('Export attempt without details or time range');
          return res.status(HttpStatus.BAD_REQUEST).json({
            code: 400,
            data: null,
            message:
              '请选择明细进行导出，或提供起始时间和终止时间进行时间范围导出',
          });
        }
      }

      // 构建导出参数
      const exportParams: any = {};

      if (startTime) {
        exportParams.startTime = decodeURIComponent(startTime);
      }

      if (endTime) {
        exportParams.endTime = decodeURIComponent(endTime);
      }

      // 处理明细ID列表（如果提供了的话）
      if (detailIds && detailIds.trim() !== '') {
        const decodedDetailIds = decodeURIComponent(detailIds);
        const detailIdArray = decodedDetailIds
          .split(',')
          .map((id: string) => id.trim())
          .filter((id: string) => id.length > 0);

        // 验证处理后的ID数组
        if (detailIdArray.length === 0) {
          this.logger.warn(
            'Export attempt with empty detail IDs after processing',
          );
          return res.status(HttpStatus.BAD_REQUEST).json({
            code: 400,
            data: null,
            message: '明细ID格式不正确',
          });
        }

        exportParams.detailIds = detailIdArray;
        this.logger.log(
          `Exporting ${detailIdArray.length} selected income details`,
        );
      } else {
        this.logger.log(
          `Exporting income details by time range: ${startTime} to ${endTime}`,
        );
      }

      if (customerSearch) {
        exportParams.customerSearch = decodeURIComponent(customerSearch);
      }

      if (responsibleUserSearch) {
        exportParams.responsibleUserSearch = decodeURIComponent(
          responsibleUserSearch,
        );
      }

      if (companyCode) {
        exportParams.companyCode = decodeURIComponent(companyCode);
      }

      const excelBuffer =
        await this.incomesService.exportDetailsToExcel(exportParams);

      // 设置响应头
      res.setHeader(
        'Content-Type',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      );
      res.setHeader(
        'Content-Disposition',
        `attachment; filename="income-details-${Date.now()}.xlsx"`,
      );

      // 发送Excel文件
      res.send(excelBuffer);
    } catch (error) {
      this.logger.error('Excel export failed', error);
      res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        code: 500,
        data: null,
        message: 'Excel导出失败',
      });
    }
  }
}
