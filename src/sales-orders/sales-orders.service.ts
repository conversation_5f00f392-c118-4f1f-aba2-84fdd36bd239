import {
  Injectable,
  Logger,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { SalesOrder, SalesOrderStatus } from './entities/sales-order.entity';
import { SalesOrderDetail } from './entities/sales-order-detail.entity';
import { SalesOrderCustomer } from './entities/sales-order-customer.entity';
import { SkuInventory } from '@/skus-inventory/entities/sku-inventory.entity';
import { User } from '@/users/entities/user.entity';
import { Customer } from '@/customers/entities/customer.entity';
import { CreateSalesOrderDto } from './dto/create-sales-order.dto';
import {
  UpdateSalesOrderDto,
  UpdateSalesOrderStatusDto,
} from './dto/update-sales-order.dto';
import { QuerySalesOrdersDto } from './dto/query-sales-orders.dto';
import {
  SalesOrderResponseDto,
  SalesOrderListResponseDto,
} from './dto/sales-order-response.dto';
import { ExpensesService } from '@/expenses/expenses.service';
import { ShippingMethod } from './entities/sales-order.entity';

@Injectable()
export class SalesOrdersService {
  private readonly logger = new Logger(SalesOrdersService.name);

  constructor(
    @InjectRepository(SalesOrder)
    private salesOrderRepository: Repository<SalesOrder>,
    @InjectRepository(SkuInventory)
    private skuInventoryRepository: Repository<SkuInventory>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectRepository(Customer)
    private customerRepository: Repository<Customer>,
    private dataSource: DataSource,
    private expensesService: ExpensesService,
  ) {}

  /**
   * 创建销售订单
   */
  async create(createDto: CreateSalesOrderDto): Promise<void> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // 1. 验证销售人员是否存在
      const salesPerson = await this.userRepository.findOne({
        where: { code: createDto.salesPersonCode, isDeleted: false },
      });
      if (!salesPerson) {
        throw new NotFoundException(
          `销售人员 ${createDto.salesPersonCode} 不存在`,
        );
      }

      // 2. 验证主客户是否存在
      const customer = await this.customerRepository.findOne({
        where: { code: createDto.customerCode, isDeleted: false },
      });
      if (!customer) {
        throw new NotFoundException(`客户 ${createDto.customerCode} 不存在`);
      }

      // 2.5. 如果指定了发货人，验证发货人是否存在
      let shipper: User | null = null;
      if (createDto.shipperUserCode) {
        shipper = await this.userRepository.findOne({
          where: { code: createDto.shipperUserCode, isDeleted: false },
        });
        if (!shipper) {
          throw new NotFoundException(
            `发货人 ${createDto.shipperUserCode} 不存在`,
          );
        }
      }

      // 3. 如果是代发模式，验证代发客户是否存在
      let dropShipCustomer: Customer | null = null;
      if (createDto.isDropShipping && createDto.dropShipCustomerCode) {
        dropShipCustomer = await this.customerRepository.findOne({
          where: { code: createDto.dropShipCustomerCode, isDeleted: false },
        });
        if (!dropShipCustomer) {
          throw new NotFoundException(
            `代发客户 ${createDto.dropShipCustomerCode} 不存在`,
          );
        }
      }

      // 4. 验证库存并检查库存是否足够
      const inventoryChecks = await Promise.all(
        createDto.details.map(async (detail) => {
          const inventory = await this.skuInventoryRepository.findOne({
            where: { id: detail.inventoryId, isDeleted: false },
            relations: ['sku'],
          });

          if (!inventory) {
            throw new NotFoundException(
              `库存记录 ${detail.inventoryId} 不存在`,
            );
          }

          if (inventory.currentStock < detail.quantity) {
            throw new BadRequestException(
              `SKU ${inventory.sku?.code || 'Unknown'} 库存不足，当前库存：${inventory.currentStock}，需要：${detail.quantity}`,
            );
          }

          if (inventory.currentStock === 0) {
            throw new BadRequestException(
              `SKU ${inventory.sku?.code || 'Unknown'} 库存为0，不允许开销售订单`,
            );
          }

          return { inventory, detail };
        }),
      );

      // 5. 生成订单编号
      const orderNumber = await this.generateOrderNumber();

      // 6. 计算总金额
      let totalQuantity = 0;
      let totalAmount = 0;
      const detailsWithCalculations = createDto.details.map((detail) => {
        const discountAmount = detail.discountAmount || 0;
        const itemTotalAmount = detail.unitPrice * detail.quantity;
        const actualAmount = itemTotalAmount - discountAmount;

        totalQuantity += detail.quantity;
        totalAmount += actualAmount;

        return {
          ...detail,
          totalAmount: itemTotalAmount,
          actualAmount,
          discountAmount,
        };
      });

      const shippingFee = createDto.shippingFee || 0;
      const grandTotal = totalAmount + shippingFee;

      // 7. 创建销售订单主记录
      const salesOrder = queryRunner.manager.create(SalesOrder, {
        orderNumber,
        salesPersonCode: createDto.salesPersonCode,
        orderDate: new Date(createDto.orderDate),
        expectedDeliveryDate: createDto.expectedDeliveryDate
          ? new Date(createDto.expectedDeliveryDate)
          : null,
        priority: createDto.priority,
        isDropShipping: createDto.isDropShipping || false,
        isReleased: createDto.isReleased || false,
        shippingMethod: createDto.shippingMethod,
        shippingFee,
        shippingCompany: createDto.shippingCompany,
        shipperUserCode: createDto.shipperUserCode,
        customerCode: createDto.customerCode,
        dropShipCustomerCode: createDto.dropShipCustomerCode,
        totalQuantity,
        totalAmount,
        grandTotal,
        createdByUserCode: createDto.salesPersonCode,
        remark: createDto.remark,
      });

      const savedOrder = await queryRunner.manager.save(salesOrder);

      // 8. 创建销售订单明细
      await Promise.all(
        detailsWithCalculations.map(async (detail, index) => {
          const { inventory } = inventoryChecks[index];

          // 计算成本相关字段
          const costPrice = inventory.sku?.clothingCost || 0;
          const totalCost = costPrice * detail.quantity;
          const grossProfit = detail.actualAmount - totalCost;

          const orderDetail = queryRunner.manager.create(SalesOrderDetail, {
            salesOrderId: savedOrder.id,
            inventoryId: detail.inventoryId,
            productCode: inventory.sku?.code || '', // SKU表中没有productCode，使用code
            colorCode: inventory.sku?.colorCode || '',
            sizeCode: inventory.size,
            skuCode: inventory.sku?.code || '',
            productName: inventory.sku?.name,
            colorName: inventory.sku?.color?.name || null,
            quantity: detail.quantity,
            pendingShipQuantity: detail.quantity,
            stockAtOrder: inventory.currentStock,
            priceType: detail.priceType,
            unitPrice: detail.unitPrice,
            totalAmount: detail.totalAmount,
            discountAmount: detail.discountAmount,
            actualAmount: detail.actualAmount,
            costPrice,
            totalCost,
            grossProfit,
            categoryCode: inventory.sku?.categoryCode,
            brandCode: inventory.sku?.brandCode,
            supplierCode: inventory.sku?.supplierCode,
            expectedShipDate: detail.expectedShipDate
              ? new Date(detail.expectedShipDate)
              : null,
            remark: detail.remark,
          });

          return queryRunner.manager.save(orderDetail);
        }),
      );

      // 9. 预留库存
      await Promise.all(
        inventoryChecks.map(async ({ inventory, detail }) => {
          await queryRunner.manager.update(
            SkuInventory,
            { id: inventory.id },
            {
              currentStock: inventory.currentStock - detail.quantity,
              isOutOfStock: inventory.currentStock - detail.quantity <= 0,
            },
          );
        }),
      );

      // 10. 如果提供了客户关联信息，创建客户关联记录
      if (createDto.customers && createDto.customers.length > 0) {
        await Promise.all(
          createDto.customers.map(async (customerDto) => {
            const customerRecord = queryRunner.manager.create(
              SalesOrderCustomer,
              {
                salesOrderId: savedOrder.id,
                customerCode: customerDto.customerCode,
                customerName: customer.name,
                allocatedQuantity: customerDto.allocatedQuantity || 0,
                allocatedAmount: customerDto.allocatedAmount || 0,
                allocatedShippingFee: customerDto.allocatedShippingFee || 0,
                totalPayable:
                  (customerDto.allocatedAmount || 0) +
                  (customerDto.allocatedShippingFee || 0),
                receiverName: customerDto.receiverName,
                receiverPhone: customerDto.receiverPhone,
                shippingAddress: customerDto.shippingAddress,
                provinceCode: customerDto.provinceCode,
                city: customerDto.city,
                district: customerDto.district,
                priority: customerDto.priority || 0,
                isUrgent: customerDto.isUrgent || false,
                specialRequirements: customerDto.specialRequirements,
                remark: customerDto.remark,
              },
            );

            return queryRunner.manager.save(customerRecord);
          }),
        );
      }

      await queryRunner.commitTransaction();

      this.logger.log(
        `销售订单创建成功: ${orderNumber}, 总金额: ${grandTotal}`,
      );
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(`创建销售订单失败: ${error.message}`, error.stack);
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * 生成订单编号
   */
  private async generateOrderNumber(): Promise<string> {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const datePrefix = `SO${year}${month}${day}`;

    // 查找当天最大的订单编号
    const lastOrder = await this.salesOrderRepository
      .createQueryBuilder('order')
      .where('order.orderNumber LIKE :prefix', { prefix: `${datePrefix}%` })
      .orderBy('order.orderNumber', 'DESC')
      .getOne();

    let sequence = 1;
    if (lastOrder) {
      const lastSequence = parseInt(
        lastOrder.orderNumber.substring(datePrefix.length),
      );
      sequence = lastSequence + 1;
    }

    return `${datePrefix}${String(sequence).padStart(4, '0')}`;
  }

  /**
   * 分页查询销售订单
   */
  async findAll(
    queryDto: QuerySalesOrdersDto,
  ): Promise<SalesOrderListResponseDto> {
    const {
      page = 1,
      pageSize = 10,
      sortField = 'createdAt',
      sortOrder = 'DESC',
      includeDetails = false,
      includeCustomers = false,
      ...filters
    } = queryDto;

    const queryBuilder = this.salesOrderRepository
      .createQueryBuilder('order')
      .leftJoinAndSelect('order.salesPerson', 'salesPerson')
      .leftJoinAndSelect('order.customer', 'customer')
      .leftJoinAndSelect('order.dropShipCustomer', 'dropShipCustomer')
      .leftJoinAndSelect('order.shipper', 'shipper')
      .where('order.isDeleted = :isDeleted', { isDeleted: false });

    // 添加关联查询
    if (includeDetails) {
      queryBuilder.leftJoinAndSelect('order.details', 'details');
    }
    if (includeCustomers) {
      queryBuilder.leftJoinAndSelect('order.customers', 'customers');
    }

    // 应用过滤条件
    this.applyFilters(queryBuilder, filters);

    // 应用排序
    queryBuilder.orderBy(`order.${sortField}`, sortOrder);

    // 应用分页
    const skip = (page - 1) * pageSize;
    queryBuilder.skip(skip).take(pageSize);

    const [orders, total] = await queryBuilder.getManyAndCount();

    const data = orders.map((order) => this.transformToResponseDto(order));

    return {
      data,
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize),
    };
  }

  /**
   * 应用查询过滤条件
   */
  private applyFilters(queryBuilder: any, filters: any): void {
    if (filters.orderNumber) {
      queryBuilder.andWhere('order.orderNumber LIKE :orderNumber', {
        orderNumber: `%${filters.orderNumber}%`,
      });
    }

    if (filters.salesPersonCode) {
      queryBuilder.andWhere('order.salesPersonCode = :salesPersonCode', {
        salesPersonCode: filters.salesPersonCode,
      });
    }

    if (filters.customerCode) {
      queryBuilder.andWhere('order.customerCode = :customerCode', {
        customerCode: filters.customerCode,
      });
    }

    if (filters.status) {
      queryBuilder.andWhere('order.status = :status', {
        status: filters.status,
      });
    }

    if (filters.priority) {
      queryBuilder.andWhere('order.priority = :priority', {
        priority: filters.priority,
      });
    }

    if (filters.shippingMethod) {
      queryBuilder.andWhere('order.shippingMethod = :shippingMethod', {
        shippingMethod: filters.shippingMethod,
      });
    }

    if (filters.isDropShipping !== undefined) {
      queryBuilder.andWhere('order.isDropShipping = :isDropShipping', {
        isDropShipping: filters.isDropShipping,
      });
    }

    if (filters.isReleased !== undefined) {
      queryBuilder.andWhere('order.isReleased = :isReleased', {
        isReleased: filters.isReleased,
      });
    }

    if (filters.orderDateStart) {
      queryBuilder.andWhere('order.orderDate >= :orderDateStart', {
        orderDateStart: filters.orderDateStart,
      });
    }

    if (filters.orderDateEnd) {
      queryBuilder.andWhere('order.orderDate <= :orderDateEnd', {
        orderDateEnd: filters.orderDateEnd,
      });
    }

    if (filters.createdAtStart) {
      queryBuilder.andWhere('order.createdAt >= :createdAtStart', {
        createdAtStart: filters.createdAtStart,
      });
    }

    if (filters.createdAtEnd) {
      queryBuilder.andWhere('order.createdAt <= :createdAtEnd', {
        createdAtEnd: filters.createdAtEnd,
      });
    }

    if (filters.totalAmountMin !== undefined) {
      queryBuilder.andWhere('order.totalAmount >= :totalAmountMin', {
        totalAmountMin: filters.totalAmountMin,
      });
    }

    if (filters.totalAmountMax !== undefined) {
      queryBuilder.andWhere('order.totalAmount <= :totalAmountMax', {
        totalAmountMax: filters.totalAmountMax,
      });
    }

    if (filters.shippingCompany) {
      queryBuilder.andWhere('order.shippingCompany LIKE :shippingCompany', {
        shippingCompany: `%${filters.shippingCompany}%`,
      });
    }

    if (filters.trackingNumber) {
      queryBuilder.andWhere('order.trackingNumber LIKE :trackingNumber', {
        trackingNumber: `%${filters.trackingNumber}%`,
      });
    }
  }

  /**
   * 根据ID查询销售订单详情
   */
  async findOne(id: string): Promise<SalesOrderResponseDto> {
    const order = await this.salesOrderRepository.findOne({
      where: { id, isDeleted: false },
      relations: [
        'salesPerson',
        'customer',
        'dropShipCustomer',
        'shipper',
        'details',
        'customers',
      ],
    });

    if (!order) {
      throw new NotFoundException(`销售订单 ${id} 不存在`);
    }

    return this.transformToResponseDto(order);
  }

  /**
   * 更新销售订单基本信息（仅草稿状态可编辑）
   */
  async update(id: string, updateDto: UpdateSalesOrderDto): Promise<void> {
    const order = await this.salesOrderRepository.findOne({
      where: { id, isDeleted: false },
    });

    if (!order) {
      throw new NotFoundException(`销售订单 ${id} 不存在`);
    }

    if (order.status !== SalesOrderStatus.DRAFT) {
      throw new BadRequestException('只有草稿状态的订单才能编辑基本信息');
    }

    // 如果是代发模式，验证代发客户
    if (updateDto.isDropShipping && updateDto.dropShipCustomerCode) {
      const dropShipCustomer = await this.customerRepository.findOne({
        where: { code: updateDto.dropShipCustomerCode, isDeleted: false },
      });
      if (!dropShipCustomer) {
        throw new NotFoundException(
          `代发客户 ${updateDto.dropShipCustomerCode} 不存在`,
        );
      }
    }

    // 如果指定了发货人，验证发货人是否存在
    if (updateDto.shipperUserCode) {
      const shipper = await this.userRepository.findOne({
        where: { code: updateDto.shipperUserCode, isDeleted: false },
      });
      if (!shipper) {
        throw new NotFoundException(
          `发货人 ${updateDto.shipperUserCode} 不存在`,
        );
      }
    }

    // 更新订单信息
    await this.salesOrderRepository.update(id, {
      expectedDeliveryDate: updateDto.expectedDeliveryDate
        ? new Date(updateDto.expectedDeliveryDate)
        : undefined,
      priority: updateDto.priority,
      isDropShipping: updateDto.isDropShipping,
      isReleased: updateDto.isReleased,
      shippingMethod: updateDto.shippingMethod,
      shippingFee: updateDto.shippingFee,
      shippingCompany: updateDto.shippingCompany,
      trackingNumber: updateDto.trackingNumber,
      shipperUserCode: updateDto.shipperUserCode,
      dropShipCustomerCode: updateDto.dropShipCustomerCode,
      remark: updateDto.remark,
    });

    // 如果运费发生变化，重新计算总金额
    if (updateDto.shippingFee !== undefined) {
      await this.recalculateOrderTotal(id);
    }

    this.logger.log(`销售订单 ${id} 更新成功`);
  }

  /**
   * 更新销售订单状态
   */
  async updateStatus(
    id: string,
    updateStatusDto: UpdateSalesOrderStatusDto,
  ): Promise<void> {
    const order = await this.salesOrderRepository.findOne({
      where: { id, isDeleted: false },
    });

    if (!order) {
      throw new NotFoundException(`销售订单 ${id} 不存在`);
    }

    // 验证状态转换是否合法
    this.validateStatusTransition(order.status, updateStatusDto.status);

    const updateData: any = {
      status: updateStatusDto.status,
    };

    // 如果状态变更为待发货，记录确认信息
    if (updateStatusDto.status === SalesOrderStatus.PENDING_SHIPMENT) {
      if (!updateStatusDto.confirmedByUserCode) {
        throw new BadRequestException('确认人编码不能为空');
      }

      const confirmedUser = await this.userRepository.findOne({
        where: { code: updateStatusDto.confirmedByUserCode, isDeleted: false },
      });
      if (!confirmedUser) {
        throw new NotFoundException(
          `确认人 ${updateStatusDto.confirmedByUserCode} 不存在`,
        );
      }

      updateData.confirmedByUserCode = updateStatusDto.confirmedByUserCode;
      updateData.confirmedAt = new Date();
    }

    if (updateStatusDto.remark) {
      updateData.remark = updateStatusDto.remark;
    }

    await this.salesOrderRepository.update(id, updateData);

    // 如果订单状态变更为已完成且物流方式为寄付，自动创建财务支出记录
    if (updateStatusDto.status === SalesOrderStatus.COMPLETED) {
      await this.handleCompletedOrderFinancialRecord(order);
    }

    this.logger.log(
      `销售订单 ${id} 状态更新: ${order.status} -> ${updateStatusDto.status}`,
    );
  }

  /**
   * 处理已完成订单的财务记录
   * 当订单完成且物流方式为寄付时，自动创建财务支出记录
   */
  private async handleCompletedOrderFinancialRecord(
    order: SalesOrder,
  ): Promise<void> {
    // 检查是否为寄付模式且运费大于0
    if (
      order.shippingMethod === ShippingMethod.PREPAID &&
      order.shippingFee > 0
    ) {
      try {
        // 获取完整的订单信息（包含客户和销售人员信息）
        const fullOrder = await this.salesOrderRepository.findOne({
          where: { id: order.id, isDeleted: false },
          relations: ['salesPerson', 'customer'],
        });

        if (!fullOrder) {
          this.logger.warn(
            `无法找到订单 ${order.id} 的完整信息，跳过财务记录创建`,
          );
          return;
        }

        // 构建备注信息
        const customerName = fullOrder.customer?.name || fullOrder.customerCode;
        const salesPersonName =
          fullOrder.salesPerson?.nickname || fullOrder.salesPersonCode;
        const remark = `销售订单寄付运费 - 客户：${customerName} - 销售员：${salesPersonName} - 订单号：${fullOrder.orderNumber}`;

        // 创建财务支出记录
        await this.expensesService.createDetail({
          supplierCode: 'LOGISTICS', // 使用物流供应商编码
          companyCode: '01', // 默认使用公司编码'01'
          amount: order.shippingFee,
          createDate: new Date().toISOString().split('T')[0], // 当前日期
          remark,
          screenshot: undefined, // 自动创建的记录不包含截图
        });

        this.logger.log(
          `已为订单 ${fullOrder.orderNumber} 创建财务支出记录，金额：${order.shippingFee}`,
        );
      } catch (error) {
        // 财务记录创建失败不应影响订单状态更新
        this.logger.error(
          `为订单 ${order.orderNumber || order.id} 创建财务支出记录失败: ${error.message}`,
          error.stack,
        );
      }
    }
  }

  /**
   * 验证状态转换是否合法
   */
  private validateStatusTransition(
    currentStatus: SalesOrderStatus,
    newStatus: SalesOrderStatus,
  ): void {
    const validTransitions: Record<SalesOrderStatus, SalesOrderStatus[]> = {
      [SalesOrderStatus.DRAFT]: [
        SalesOrderStatus.PENDING_SHIPMENT,
        SalesOrderStatus.CANCELLED,
      ],
      [SalesOrderStatus.PENDING_SHIPMENT]: [
        SalesOrderStatus.COMPLETED,
        SalesOrderStatus.CANCELLED,
      ],
      [SalesOrderStatus.COMPLETED]: [], // 完成状态不能转换
      [SalesOrderStatus.CANCELLED]: [], // 取消状态不能转换
    };

    const allowedStatuses = validTransitions[currentStatus] || [];
    if (!allowedStatuses.includes(newStatus)) {
      throw new BadRequestException(
        `不能从状态 ${currentStatus} 转换到 ${newStatus}`,
      );
    }
  }

  /**
   * 重新计算订单总金额
   */
  private async recalculateOrderTotal(orderId: string): Promise<void> {
    const order = await this.salesOrderRepository.findOne({
      where: { id: orderId },
      relations: ['details'],
    });

    if (!order) {
      return;
    }

    const totalQuantity = order.details.reduce(
      (sum, detail) => sum + detail.quantity,
      0,
    );
    const totalAmount = order.details.reduce(
      (sum, detail) => sum + detail.actualAmount,
      0,
    );
    const grandTotal = totalAmount + order.shippingFee;

    await this.salesOrderRepository.update(orderId, {
      totalQuantity,
      totalAmount,
      grandTotal,
    });
  }

  /**
   * 软删除销售订单
   */
  async remove(id: string): Promise<void> {
    const order = await this.salesOrderRepository.findOne({
      where: { id, isDeleted: false },
    });

    if (!order) {
      throw new NotFoundException(`销售订单 ${id} 不存在`);
    }

    if (order.status === SalesOrderStatus.COMPLETED) {
      throw new BadRequestException('已完成的订单不能删除');
    }

    await this.salesOrderRepository.update(id, {
      isDeleted: true,
      deletedAt: new Date(),
    });

    this.logger.log(`销售订单 ${id} 删除成功`);
  }

  /**
   * 转换为响应DTO
   */
  private transformToResponseDto(order: SalesOrder): SalesOrderResponseDto {
    return {
      id: order.id,
      orderNumber: order.orderNumber,
      salesPersonCode: order.salesPersonCode,
      salesPersonName: order.salesPerson?.nickname || null,
      orderDate: order.orderDate,
      expectedDeliveryDate: order.expectedDeliveryDate,
      priority: order.priority,
      status: order.status,
      isDropShipping: order.isDropShipping,
      isReleased: order.isReleased,
      shippingMethod: order.shippingMethod,
      shippingFee: order.shippingFee,
      shippingCompany: order.shippingCompany,
      trackingNumber: order.trackingNumber,
      shipperUserCode: order.shipperUserCode,
      shipperUserName: order.shipper?.nickname || null,
      customerCode: order.customerCode,
      customerName: order.customer?.name || null,
      dropShipCustomerCode: order.dropShipCustomerCode,
      dropShipCustomerName: order.dropShipCustomer?.name || null,
      totalQuantity: order.totalQuantity,
      totalAmount: order.totalAmount,
      grandTotal: order.grandTotal,
      createdByUserCode: order.createdByUserCode,
      confirmedByUserCode: order.confirmedByUserCode,
      confirmedAt: order.confirmedAt,
      remark: order.remark,
      createdAt: order.createdAt,
      updatedAt: order.updatedAt,
      details: order.details?.map((detail) => ({
        id: detail.id,
        inventoryId: detail.inventoryId,
        productCode: detail.productCode,
        colorCode: detail.colorCode,
        sizeCode: detail.sizeCode,
        skuCode: detail.skuCode,
        productName: detail.productName,
        colorName: detail.colorName,
        quantity: detail.quantity,
        shippedQuantity: detail.shippedQuantity,
        pendingShipQuantity: detail.pendingShipQuantity,
        reservedQuantity: detail.reservedQuantity,
        stockAtOrder: detail.stockAtOrder,
        priceType: detail.priceType,
        unitPrice: detail.unitPrice,
        totalAmount: detail.totalAmount,
        discountAmount: detail.discountAmount,
        actualAmount: detail.actualAmount,
        costPrice: detail.costPrice,
        totalCost: detail.totalCost,
        grossProfit: detail.grossProfit,
        categoryCode: detail.categoryCode,
        brandCode: detail.brandCode,
        supplierCode: detail.supplierCode,
        expectedShipDate: detail.expectedShipDate,
        remark: detail.remark,
        createdAt: detail.createdAt,
        updatedAt: detail.updatedAt,
      })),
      customers: order.customers?.map((customer) => ({
        id: customer.id,
        customerCode: customer.customerCode,
        customerName: customer.customerName,
        allocatedQuantity: customer.allocatedQuantity,
        allocatedAmount: customer.allocatedAmount,
        allocatedShippingFee: customer.allocatedShippingFee,
        totalPayable: customer.totalPayable,
        status: customer.status,
        receiverName: customer.receiverName,
        receiverPhone: customer.receiverPhone,
        shippingAddress: customer.shippingAddress,
        provinceCode: customer.provinceCode,
        city: customer.city,
        district: customer.district,
        shippingCompany: customer.shippingCompany,
        trackingNumber: customer.trackingNumber,
        shippedAt: customer.shippedAt,
        deliveredAt: customer.deliveredAt,
        priority: customer.priority,
        isUrgent: customer.isUrgent,
        specialRequirements: customer.specialRequirements,
        remark: customer.remark,
        createdAt: customer.createdAt,
        updatedAt: customer.updatedAt,
      })),
    };
  }
}
