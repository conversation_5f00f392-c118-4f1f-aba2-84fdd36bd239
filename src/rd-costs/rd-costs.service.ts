import {
  Injectable,
  Logger,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import * as ExcelJS from 'exceljs';
import axios from 'axios';
import { RdCost } from './entities/rd-cost.entity';
import { RdCostDetail } from './entities/rd-cost-detail.entity';
import { CreateRdCostDetailDto } from './dto/create-rd-cost-detail.dto';
import { UpdateRdCostDetailDto } from './dto/update-rd-cost-detail.dto';
import { QueryRdCostDetailsDto } from './dto/query-rd-cost-details.dto';
import { CompaniesService } from '../companies/companies.service';

// 内部处理接口
interface ProcessedExportDto {
  startTime?: string;
  endTime?: string;
  detailIds?: string[];
  companyCode?: string;
}

@Injectable()
export class RdCostsService {
  private readonly logger = new Logger(RdCostsService.name);

  constructor(
    @InjectRepository(RdCost)
    private readonly rdCostRepository: Repository<RdCost>,
    @InjectRepository(RdCostDetail)
    private readonly rdCostDetailRepository: Repository<RdCostDetail>,
    private readonly dataSource: DataSource,
    private readonly companiesService: CompaniesService,
  ) {}

  // 获取或创建研发成本记录（系统只有一条记录）
  private async getOrCreateRdCost(): Promise<RdCost> {
    let rdCost = await this.rdCostRepository.findOne({
      where: { isDeleted: false },
    });

    if (!rdCost) {
      rdCost = this.rdCostRepository.create({
        totalAmount: 0,
        createDate: new Date().toISOString().split('T')[0],
      });
      await this.rdCostRepository.save(rdCost);
      this.logger.log('Created new rd cost record');
    }

    return rdCost;
  }

  // 验证公司是否存在
  private async validateCompany(
    companyCode: string,
  ): Promise<{ code: string; name: string }> {
    if (!companyCode || companyCode.trim() === '') {
      throw new BadRequestException('公司编码不能为空');
    }

    const company = await this.companiesService.findByCode(companyCode);
    if (!company) {
      throw new BadRequestException(`公司 ${companyCode} 不存在`);
    }

    return { code: company.code, name: company.name };
  }

  // 新增研发成本明细
  async createDetail(createDetailDto: CreateRdCostDetailDto): Promise<void> {
    const { companyCode, amount, screenshot, remark, createDate } =
      createDetailDto;

    this.logger.log(
      `Creating rd cost detail with company: ${companyCode}, amount: ${amount}`,
    );

    // 验证公司
    const company = await this.validateCompany(companyCode);

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // 获取或创建研发成本记录
      const rdCost = await this.getOrCreateRdCost();

      // 如果没有提供 createDate，使用当前日期
      const finalCreateDate =
        createDate || new Date().toISOString().split('T')[0];

      // 创建明细记录
      const detail = queryRunner.manager.create(RdCostDetail, {
        rdCostId: rdCost.id,
        companyCode: company.code,
        amount,
        screenshot,
        remark,
        createDate: finalCreateDate,
      });

      await queryRunner.manager.save(detail);

      // 更新总金额
      await queryRunner.manager.update(
        RdCost,
        { id: rdCost.id },
        {
          totalAmount: () => `"totalAmount" + ${amount}`,
        },
      );

      await queryRunner.commitTransaction();
      this.logger.log(
        `Rd cost detail created successfully with amount: ${amount}`,
      );
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  // 分页查询研发成本明细
  async findAllDetails(queryDto: QueryRdCostDetailsDto) {
    const {
      page = 1,
      pageSize = 10,
      startTime,
      endTime,
      search,
      sortBy = 'createDate',
      sortOrder = 'DESC',
    } = queryDto;

    this.logger.log(
      `Querying rd cost details: page=${page}, pageSize=${pageSize}`,
    );

    // 获取研发成本总金额
    const rdCost = await this.getOrCreateRdCost();

    // 处理时间参数
    let processedStartTime = startTime;
    let processedEndTime = endTime;

    if (startTime) {
      // 如果是日期格式（如 2025-05-01），转换为当天开始时间
      if (startTime.length === 10) {
        processedStartTime = `${startTime}T00:00:00.000Z`;
      }
    }

    if (endTime) {
      // 如果是日期格式（如 2025-05-31），转换为当天结束时间
      if (endTime.length === 10) {
        processedEndTime = `${endTime}T23:59:59.999Z`;
      }
    }

    // 构建查询条件
    const queryBuilder = this.rdCostDetailRepository
      .createQueryBuilder('detail')
      .leftJoin('companies', 'company', 'company.code = detail.companyCode')
      .addSelect('company.name', 'companyName')
      .where('detail.isDeleted = false');

    // 添加时间范围过滤 - 由于createDate是字符串类型，使用CAST转换为日期进行比较
    if (processedStartTime) {
      queryBuilder.andWhere(
        'CAST(detail.createDate AS DATE) >= CAST(:startTime AS DATE)',
        {
          startTime: processedStartTime,
        },
      );
    }

    if (processedEndTime) {
      queryBuilder.andWhere(
        'CAST(detail.createDate AS DATE) <= CAST(:endTime AS DATE)',
        {
          endTime: processedEndTime,
        },
      );
    }

    // 添加搜索过滤
    if (search) {
      queryBuilder.andWhere('detail.remark ILIKE :search', {
        search: `%${search}%`,
      });
    }

    // 排序
    queryBuilder.orderBy(`detail.${sortBy}`, sortOrder as 'ASC' | 'DESC');

    // 处理分页参数
    if (page === 0 && pageSize === 0) {
      // 返回所有数据
      const result = await queryBuilder.getRawAndEntities();
      const details = result.entities.map((detail, index) => ({
        ...detail,
        companyName: result.raw[index]?.companyName || null,
      }));

      // 计算查询结果的总金额
      const queryResultTotalAmount = details.reduce(
        (sum, detail) => sum + detail.amount,
        0,
      );

      return {
        details,
        total: details.length,
        page: 0,
        pageSize: 0,
        totalAmount: queryResultTotalAmount,
        systemTotalAmount: rdCost.totalAmount,
        startTime: processedStartTime || null,
        endTime: processedEndTime || null,
      };
    }

    // 正常分页查询
    const paginatedResult = await queryBuilder
      .skip((page - 1) * pageSize)
      .take(pageSize)
      .getRawAndEntities();

    const details = paginatedResult.entities.map((detail, index) => ({
      ...detail,
      companyName: paginatedResult.raw[index]?.companyName || null,
    }));

    // 获取总数
    const total = await queryBuilder.getCount();

    // 计算所有查询条件下的总金额（不分页）
    const allMatchingResult = await queryBuilder
      .skip(0)
      .take(undefined)
      .getRawAndEntities();

    const queryResultTotalAmount = allMatchingResult.entities.reduce(
      (sum, detail) => sum + detail.amount,
      0,
    );

    return {
      details,
      total,
      page,
      pageSize,
      totalAmount: queryResultTotalAmount,
      systemTotalAmount: rdCost.totalAmount,
      startTime: processedStartTime || null,
      endTime: processedEndTime || null,
    };
  }

  // 获取研发成本明细详情
  async findDetailById(id: string): Promise<RdCostDetail> {
    this.logger.log(`Finding rd cost detail by id: ${id}`);

    const detail = await this.rdCostDetailRepository.findOne({
      where: { id, isDeleted: false },
    });

    if (!detail) {
      throw new NotFoundException('研发成本明细不存在');
    }

    return detail;
  }

  // 更新研发成本明细
  async updateDetail(
    id: string,
    updateDetailDto: UpdateRdCostDetailDto,
  ): Promise<void> {
    this.logger.log(`Updating rd cost detail: ${id}`);

    // 如果更新了公司编码，需要验证
    if (updateDetailDto.companyCode) {
      await this.validateCompany(updateDetailDto.companyCode);
    }

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // 查找现有明细
      const existingDetail = await queryRunner.manager.findOne(RdCostDetail, {
        where: { id, isDeleted: false },
      });

      if (!existingDetail) {
        throw new NotFoundException('研发成本明细不存在');
      }

      const oldAmount = existingDetail.amount;
      const newAmount = updateDetailDto.amount ?? oldAmount;
      const amountDifference = newAmount - oldAmount;

      // 更新明细记录
      await queryRunner.manager.update(RdCostDetail, { id }, updateDetailDto);

      // 如果金额有变化，更新总金额
      if (amountDifference !== 0) {
        const rdCost = await this.getOrCreateRdCost();
        await queryRunner.manager.update(
          RdCost,
          { id: rdCost.id },
          {
            totalAmount: () => `"totalAmount" + ${amountDifference}`,
          },
        );
      }

      await queryRunner.commitTransaction();
      this.logger.log(`Rd cost detail updated successfully: ${id}`);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  // 删除研发成本明细
  async removeDetail(id: string): Promise<void> {
    this.logger.log(`Removing rd cost detail: ${id}`);

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // 查找现有明细
      const existingDetail = await queryRunner.manager.findOne(RdCostDetail, {
        where: { id, isDeleted: false },
      });

      if (!existingDetail) {
        throw new NotFoundException('研发成本明细不存在');
      }

      // 软删除明细记录
      await queryRunner.manager.update(
        RdCostDetail,
        { id },
        {
          isDeleted: true,
        },
      );

      // 从总金额中减去删除的金额
      const rdCost = await this.getOrCreateRdCost();
      await queryRunner.manager.update(
        RdCost,
        { id: rdCost.id },
        {
          totalAmount: () => `"totalAmount" - ${existingDetail.amount}`,
        },
      );

      await queryRunner.commitTransaction();
      this.logger.log(`Rd cost detail removed successfully: ${id}`);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  // 导出Excel
  async exportToExcel(exportDto?: ProcessedExportDto): Promise<Buffer> {
    this.logger.log('Exporting rd cost details to Excel');

    // 验证：如果没有提供明细ID，必须提供时间范围
    if (!exportDto?.detailIds || exportDto.detailIds.length === 0) {
      if (!exportDto?.startTime || !exportDto?.endTime) {
        throw new BadRequestException(
          '请选择明细进行导出，或提供起始时间和终止时间进行时间范围导出',
        );
      }
    }

    // 获取研发成本总金额
    const rdCost = await this.getOrCreateRdCost();

    // 构建查询条件
    const queryBuilder = this.rdCostDetailRepository
      .createQueryBuilder('detail')
      .leftJoin('companies', 'company', 'company.code = detail.companyCode')
      .where('detail.isDeleted = false')
      .orderBy('detail.createDate', 'DESC');

    // 根据导出条件过滤
    if (exportDto) {
      const { startTime, endTime, detailIds, companyCode } = exportDto;

      // 时间范围过滤 - 由于createDate是字符串类型，使用CAST转换为日期进行比较
      if (startTime) {
        queryBuilder.andWhere(
          'CAST(detail.createDate AS DATE) >= CAST(:startTime AS DATE)',
          {
            startTime: startTime,
          },
        );
      }

      if (endTime) {
        queryBuilder.andWhere(
          'CAST(detail.createDate AS DATE) <= CAST(:endTime AS DATE)',
          {
            endTime: endTime,
          },
        );
      }

      // 明细ID过滤
      if (detailIds && detailIds.length > 0) {
        queryBuilder.andWhere('detail.id IN (:...detailIds)', { detailIds });
      }

      // 公司编码过滤
      if (companyCode) {
        queryBuilder.andWhere('detail.companyCode = :companyCode', {
          companyCode,
        });
      }
    }

    const details = await queryBuilder.getMany();

    this.logger.log(`Found ${details.length} details for export`);
    if (details.length > 0) {
      this.logger.log(
        `First detail ID: ${details[0].id}, Amount: ${details[0].amount}`,
      );
    }

    // 计算选中明细的总金额
    const selectedTotalAmount = details.reduce(
      (sum, detail) => sum + detail.amount,
      0,
    );

    // 生成Excel（支持图片插入）
    try {
      // 创建工作簿
      const workbook = new ExcelJS.Workbook();
      const worksheet = workbook.addWorksheet('研发成本明细');

      // 设置列宽
      worksheet.columns = [
        { header: '序号', key: 'index', width: 8 },
        { header: '金额', key: 'amount', width: 15 },
        { header: '创建时间', key: 'createdAt', width: 20 },
        { header: '截图', key: 'screenshot', width: 30 },
        { header: '备注', key: 'remark', width: 30 },
      ];

      // 添加标题
      worksheet.mergeCells('A1:E1');
      const titleCell = worksheet.getCell('A1');
      titleCell.value = '研发成本管理报告';
      titleCell.font = { bold: true, size: 16 };
      titleCell.alignment = { horizontal: 'center' };

      // 添加概况信息
      let currentRow = 3;
      worksheet.getCell(`A${currentRow}`).value = '成本概况';
      worksheet.getCell(`A${currentRow}`).font = { bold: true, size: 14 };
      currentRow++;

      worksheet.getCell(`A${currentRow}`).value = '研发成本总金额';
      worksheet.getCell(`B${currentRow}`).value =
        `¥${rdCost.totalAmount.toFixed(2)}`;
      currentRow++;

      worksheet.getCell(`A${currentRow}`).value = '导出明细总金额';
      worksheet.getCell(`B${currentRow}`).value =
        `¥${selectedTotalAmount.toFixed(2)}`;
      currentRow++;

      worksheet.getCell(`A${currentRow}`).value = '导出明细数量';
      worksheet.getCell(`B${currentRow}`).value = `${details.length}条`;
      currentRow++;

      worksheet.getCell(`A${currentRow}`).value = '导出时间';
      worksheet.getCell(`B${currentRow}`).value = new Date().toLocaleString(
        'zh-CN',
      );
      currentRow += 2;

      // 添加明细记录标题
      worksheet.getCell(`A${currentRow}`).value = '明细记录';
      worksheet.getCell(`A${currentRow}`).font = { bold: true, size: 14 };
      currentRow++;

      // 添加表头
      const headerRow = worksheet.getRow(currentRow);
      headerRow.values = ['序号', '金额', '创建时间', '截图', '备注'];
      headerRow.font = { bold: true };
      headerRow.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFE0E0E0' },
      };
      currentRow++;

      // 添加明细数据和图片
      for (let i = 0; i < details.length; i++) {
        const detail = details[i];
        const row = worksheet.getRow(currentRow);

        // 设置行高以容纳图片
        row.height = 120;

        // 添加基本数据
        row.values = [
          i + 1,
          `¥${detail.amount.toFixed(2)}`,
          detail.createDate,
          '', // 截图列留空，用于插入图片
          detail.remark || '',
        ];

        // 下载并插入图片
        try {
          if (detail.screenshot) {
            this.logger.log(`Downloading image: ${detail.screenshot}`);

            const imageResponse = await axios.get(detail.screenshot, {
              responseType: 'arraybuffer',
              timeout: 10000,
            });

            const imageBuffer = Buffer.from(imageResponse.data);

            // 添加图片到工作簿
            const imageId = workbook.addImage({
              buffer: imageBuffer,
              extension: 'jpeg',
            });

            // 在截图列插入图片
            worksheet.addImage(imageId, {
              tl: { col: 3, row: currentRow - 1 }, // 从截图列开始
              ext: { width: 200, height: 100 }, // 图片大小
            });

            this.logger.log(`Image inserted successfully for detail ${i + 1}`);
          }
        } catch (imageError) {
          this.logger.warn(
            `Failed to load image for detail ${i + 1}: ${detail.screenshot}`,
            imageError.message,
          );
          // 如果图片加载失败，在截图列显示链接
          row.getCell(4).value = detail.screenshot;
        }

        currentRow++;
      }

      // 设置边框
      const borderStyle = {
        top: { style: 'thin' as const },
        left: { style: 'thin' as const },
        bottom: { style: 'thin' as const },
        right: { style: 'thin' as const },
      };

      // 为表格添加边框
      const tableStartRow = currentRow - details.length - 1;
      const tableEndRow = currentRow - 1;
      for (let row = tableStartRow; row <= tableEndRow; row++) {
        for (let col = 1; col <= 5; col++) {
          worksheet.getCell(row, col).border = borderStyle;
        }
      }

      // 生成Excel缓冲区
      const excelBuffer = await workbook.xlsx.writeBuffer();
      return Buffer.from(excelBuffer);
    } catch (error) {
      this.logger.error('Excel generation failed', error);
      throw new BadRequestException('Excel生成失败');
    }
  }
}
