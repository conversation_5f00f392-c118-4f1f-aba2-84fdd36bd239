import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { RdCost } from './rd-cost.entity';

@Entity('rd_cost_details')
export class RdCostDetail {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'uuid', comment: '研发成本ID' })
  rdCostId: string;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    comment: '明细金额（保留两位小数）',
    transformer: {
      to: (value: number) => value,
      from: (value: string) => parseFloat(value),
    },
  })
  amount: number;

  @Column({
    type: 'varchar',
    length: 500,
    comment: '截图URL（必填）',
  })
  screenshot: string;

  @Column({
    type: 'text',
    nullable: true,
    comment: '备注（可选）',
  })
  remark: string | null;

  @Column({
    type: 'boolean',
    default: false,
    comment: '是否已删除',
  })
  isDeleted: boolean;

  @Column({
    type: 'varchar',
    length: 20,
    nullable: true,
    comment: '创建日期（字符串格式，必填）',
    name: 'createdAt',
  })
  createDate: string;

  @Column({
    type: 'varchar',
    length: 50,
    comment: '公司编码（必填）',
  })
  companyCode: string;

  // 关联研发成本
  @ManyToOne(() => RdCost, (rdCost) => rdCost.details)
  @JoinColumn({ name: 'rdCostId' })
  rdCost: RdCost;
}
