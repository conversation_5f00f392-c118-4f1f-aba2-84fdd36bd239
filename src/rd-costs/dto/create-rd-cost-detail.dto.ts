import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  Min,
} from 'class-validator';
import { Type } from 'class-transformer';

export class CreateRdCostDetailDto {
  @ApiProperty({
    description: '公司编码（必填）',
    example: '01',
  })
  @IsString()
  @IsNotEmpty({ message: '公司编码不能为空' })
  companyCode: string;

  @ApiProperty({
    description: '明细金额（保留两位小数）',
    example: 15000.5,
  })
  @Type(() => Number)
  @IsNumber(
    { maxDecimalPlaces: 2 },
    { message: '金额必须是数字且最多保留两位小数' },
  )
  @Min(0.01, { message: '金额必须大于0' })
  amount: number;

  @ApiProperty({
    description: '截图URL（必填）',
    example: 'https://example.com/screenshot.jpg',
  })
  @IsString()
  @IsNotEmpty({ message: '截图URL不能为空' })
  screenshot: string;

  @ApiProperty({
    description: '备注（可选）',
    example: '购买样衣费用',
    required: false,
  })
  @IsOptional()
  @IsString()
  remark?: string;

  @ApiProperty({
    description: '创建日期（字符串格式，必填）',
    example: '2024-01-15',
    required: false,
  })
  @IsOptional()
  @IsString()
  createDate?: string;
}
