import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { FixedAssetsService } from './fixed-assets.service';
import { FixedAssetsController } from './fixed-assets.controller';
import { FixedAsset } from './entities/fixed-asset.entity';
import { FixedAssetDetail } from './entities/fixed-asset-detail.entity';
import { CompaniesModule } from '@/companies/companies.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([FixedAsset, FixedAssetDetail]),
    CompaniesModule,
  ],
  controllers: [FixedAssetsController],
  providers: [FixedAssetsService],
  exports: [FixedAssetsService],
})
export class FixedAssetsModule {}
