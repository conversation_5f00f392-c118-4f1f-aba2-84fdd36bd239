import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { OperatingAsset } from './operating-asset.entity';

// 运营资产类型枚举
export enum OperatingAssetType {
  HUMAN_RESOURCES = 'human_resources', // 人力资产
  WAREHOUSE_LOGISTICS = 'warehouse_logistics', // 仓储物流
  ADMINISTRATIVE_CONSUMPTION = 'administrative_consumption', // 行政消耗
  OTHER = 'other', // 其他（第四种类型）
}

// 人力资产审核状态枚举
export enum HumanResourcesAuditStatus {
  PENDING = 'pending', // 未审核
  APPROVED = 'approved', // 已审核
}

// 仓储物流收支类型枚举
export enum WarehouseLogisticsType {
  INCOME = 'income', // 收入
  EXPENSE = 'expense', // 支出
}

@Entity('operating_asset_details')
export class OperatingAssetDetail {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'uuid', comment: '运营资产ID' })
  operatingAssetId: string;

  @Column({
    type: 'enum',
    enum: OperatingAssetType,
    comment: '运营资产类型',
  })
  type: OperatingAssetType;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    comment: '明细金额（保留两位小数）',
    transformer: {
      to: (value: number) => value,
      from: (value: string) => parseFloat(value),
    },
  })
  amount: number;

  @Column({
    type: 'varchar',
    length: 500,
    comment: '截图URL（必填）',
  })
  screenshot: string;

  @Column({
    type: 'text',
    nullable: true,
    comment: '备注（可选）',
  })
  remark: string | null;

  @Column({
    type: 'enum',
    enum: HumanResourcesAuditStatus,
    nullable: true,
    comment: '人力资产审核状态（仅人力资产类型需要）',
  })
  humanResourcesAuditStatus: HumanResourcesAuditStatus | null;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: '员工信息（仅人力资产类型需要）',
  })
  employeeInfo: string | null;

  @Column({
    type: 'enum',
    enum: WarehouseLogisticsType,
    nullable: true,
    comment: '仓储物流收支类型（仅仓储物流类型需要）',
    name: 'warehouselogisticstype',
  })
  warehouseLogisticsType: WarehouseLogisticsType | null;

  @Column({
    type: 'boolean',
    default: false,
    comment: '是否已删除',
  })
  isDeleted: boolean;

  @Column({
    type: 'varchar',
    length: 20,
    nullable: true,
    comment: '创建日期（字符串格式，必填）',
    name: 'createdAt',
  })
  createDate: string;

  @Column({
    type: 'varchar',
    length: 50,
    comment: '公司编码（必填）',
  })
  companyCode: string;

  // 关联运营资产
  @ManyToOne(() => OperatingAsset, (operatingAsset) => operatingAsset.details)
  @JoinColumn({ name: 'operatingAssetId' })
  operatingAsset: OperatingAsset;
}
