import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  Min,
  IsEnum,
  ValidateIf,
} from 'class-validator';
import { Type } from 'class-transformer';
import {
  OperatingAssetType,
  HumanResourcesAuditStatus,
  WarehouseLogisticsType,
} from '../entities/operating-asset-detail.entity';

export class CreateOperatingAssetDetailDto {
  @ApiProperty({
    description: '公司编码（必填）',
    example: '01',
  })
  @IsString()
  @IsNotEmpty({ message: '公司编码不能为空' })
  companyCode: string;

  @ApiProperty({
    description: '运营资产类型',
    enum: OperatingAssetType,
    example: OperatingAssetType.HUMAN_RESOURCES,
  })
  @IsEnum(OperatingAssetType, { message: '运营资产类型无效' })
  type: OperatingAssetType;

  @ApiProperty({
    description: '明细金额（保留两位小数）',
    example: 15000.5,
  })
  @Type(() => Number)
  @IsNumber(
    { maxDecimalPlaces: 2 },
    { message: '金额必须是数字且最多保留两位小数' },
  )
  @Min(0.01, { message: '金额必须大于0' })
  amount: number;

  @ApiProperty({
    description: '截图URL（必填）',
    example: 'https://example.com/screenshot.jpg',
  })
  @IsString()
  @IsNotEmpty({ message: '截图URL不能为空' })
  screenshot: string;

  @ApiProperty({
    description: '备注（可选）',
    example: '员工工资支出',
    required: false,
  })
  @IsOptional()
  @IsString()
  remark?: string;

  @ApiProperty({
    description: '创建日期（字符串格式，必填）',
    example: '2024-01-15',
    required: false,
  })
  @IsOptional()
  @IsString()
  createDate?: string;

  @ApiProperty({
    description: '人力资产审核状态（仅人力资产类型必填）',
    enum: HumanResourcesAuditStatus,
    example: HumanResourcesAuditStatus.PENDING,
    required: false,
  })
  @ValidateIf((o) => o.type === OperatingAssetType.HUMAN_RESOURCES)
  @IsNotEmpty({ message: '人力资产类型必须填写审核状态' })
  @IsEnum(HumanResourcesAuditStatus, { message: '审核状态无效' })
  humanResourcesAuditStatus?: HumanResourcesAuditStatus;

  @ApiProperty({
    description: '员工信息（仅人力资产类型必填）',
    example: '张三 - 开发工程师',
    required: false,
  })
  @ValidateIf((o) => o.type === OperatingAssetType.HUMAN_RESOURCES)
  @IsNotEmpty({ message: '人力资产类型必须填写员工信息' })
  @IsString()
  employeeInfo?: string;

  @ApiProperty({
    description: '仓储物流收支类型（仅仓储物流类型必填）',
    enum: WarehouseLogisticsType,
    example: WarehouseLogisticsType.EXPENSE,
    required: false,
  })
  @ValidateIf((o) => o.type === OperatingAssetType.WAREHOUSE_LOGISTICS)
  @IsNotEmpty({ message: '仓储物流类型必须填写收支类型' })
  @IsEnum(WarehouseLogisticsType, { message: '收支类型无效' })
  warehouseLogisticsType?: WarehouseLogisticsType;
}
