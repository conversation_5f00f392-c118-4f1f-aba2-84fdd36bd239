-- 修复版本：为所有资产明细表添加公司编码字段
-- 执行时间：2024年

-- 首先检查表是否存在，然后添加字段
-- 1. 为支出明细表添加公司编码字段
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'expense_details') THEN
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'expense_details' AND column_name = 'companyCode') THEN
            ALTER TABLE expense_details ADD COLUMN "companyCode" VARCHAR(50);
            COMMENT ON COLUMN expense_details."companyCode" IS '公司编码（必填）';
        END IF;
    END IF;
END $$;

-- 2. 为研发成本明细表添加公司编码字段
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'rd_cost_details') THEN
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'rd_cost_details' AND column_name = 'companyCode') THEN
            ALTER TABLE rd_cost_details ADD COLUMN "companyCode" VARCHAR(50);
            COMMENT ON COLUMN rd_cost_details."companyCode" IS '公司编码（必填）';
        END IF;
    END IF;
END $$;

-- 3. 为租赁资产明细表添加公司编码字段
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'rental_asset_details') THEN
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'rental_asset_details' AND column_name = 'companyCode') THEN
            ALTER TABLE rental_asset_details ADD COLUMN "companyCode" VARCHAR(50);
            COMMENT ON COLUMN rental_asset_details."companyCode" IS '公司编码（必填）';
        END IF;
    END IF;
END $$;

-- 4. 为运营资产明细表添加公司编码字段
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'operating_asset_details') THEN
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'operating_asset_details' AND column_name = 'companyCode') THEN
            ALTER TABLE operating_asset_details ADD COLUMN "companyCode" VARCHAR(50);
            COMMENT ON COLUMN operating_asset_details."companyCode" IS '公司编码（必填）';
        END IF;
    END IF;
END $$;

-- 5. 为固定资产明细表添加公司编码字段
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'fixed_asset_details') THEN
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'fixed_asset_details' AND column_name = 'companyCode') THEN
            ALTER TABLE fixed_asset_details ADD COLUMN "companyCode" VARCHAR(50);
            COMMENT ON COLUMN fixed_asset_details."companyCode" IS '公司编码（必填）';
        END IF;
    END IF;
END $$;

-- 6. 将现有数据的公司编码设置为 '01'（安全更新）
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'expense_details') THEN
        UPDATE expense_details SET "companyCode" = '01' WHERE "companyCode" IS NULL;
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'rd_cost_details') THEN
        UPDATE rd_cost_details SET "companyCode" = '01' WHERE "companyCode" IS NULL;
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'rental_asset_details') THEN
        UPDATE rental_asset_details SET "companyCode" = '01' WHERE "companyCode" IS NULL;
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'operating_asset_details') THEN
        UPDATE operating_asset_details SET "companyCode" = '01' WHERE "companyCode" IS NULL;
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'fixed_asset_details') THEN
        UPDATE fixed_asset_details SET "companyCode" = '01' WHERE "companyCode" IS NULL;
    END IF;
END $$;

-- 7. 将字段设置为非空约束（安全设置）
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'expense_details' AND column_name = 'companyCode') THEN
        ALTER TABLE expense_details ALTER COLUMN "companyCode" SET NOT NULL;
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'rd_cost_details' AND column_name = 'companyCode') THEN
        ALTER TABLE rd_cost_details ALTER COLUMN "companyCode" SET NOT NULL;
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'rental_asset_details' AND column_name = 'companyCode') THEN
        ALTER TABLE rental_asset_details ALTER COLUMN "companyCode" SET NOT NULL;
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'operating_asset_details' AND column_name = 'companyCode') THEN
        ALTER TABLE operating_asset_details ALTER COLUMN "companyCode" SET NOT NULL;
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'fixed_asset_details' AND column_name = 'companyCode') THEN
        ALTER TABLE fixed_asset_details ALTER COLUMN "companyCode" SET NOT NULL;
    END IF;
END $$;
