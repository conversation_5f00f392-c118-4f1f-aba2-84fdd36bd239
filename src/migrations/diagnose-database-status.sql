-- 诊断脚本：检查数据库当前状态
-- 运行此脚本来了解当前数据库的状态

-- 1. 检查所有相关表是否存在
SELECT 
    'expense_details' as table_name,
    CASE WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'expense_details') 
         THEN 'EXISTS' ELSE 'NOT EXISTS' END as status
UNION ALL
SELECT 
    'rd_cost_details' as table_name,
    CASE WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'rd_cost_details') 
         THEN 'EXISTS' ELSE 'NOT EXISTS' END as status
UNION ALL
SELECT 
    'rental_asset_details' as table_name,
    CASE WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'rental_asset_details') 
         THEN 'EXISTS' ELSE 'NOT EXISTS' END as status
UNION ALL
SELECT 
    'operating_asset_details' as table_name,
    CASE WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'operating_asset_details') 
         THEN 'EXISTS' ELSE 'NOT EXISTS' END as status
UNION ALL
SELECT 
    'fixed_asset_details' as table_name,
    CASE WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'fixed_asset_details') 
         THEN 'EXISTS' ELSE 'NOT EXISTS' END as status;

-- 2. 检查companyCode字段是否存在
SELECT 
    table_name,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name IN ('expense_details', 'rd_cost_details', 'rental_asset_details', 'operating_asset_details', 'fixed_asset_details')
  AND column_name = 'companyCode'
ORDER BY table_name;

-- 3. 检查companies表是否存在
SELECT 
    'companies' as table_name,
    CASE WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'companies') 
         THEN 'EXISTS' ELSE 'NOT EXISTS' END as status;

-- 4. 如果companies表存在，检查是否有编码为'01'的公司
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'companies') THEN
        RAISE NOTICE 'Companies table exists. Checking for company code 01...';
        PERFORM * FROM companies WHERE code = '01';
        IF FOUND THEN
            RAISE NOTICE 'Company with code 01 exists.';
        ELSE
            RAISE NOTICE 'Company with code 01 does NOT exist!';
        END IF;
    ELSE
        RAISE NOTICE 'Companies table does NOT exist!';
    END IF;
END $$;

-- 5. 检查所有明细表的结构
SELECT 
    table_name,
    column_name,
    data_type,
    is_nullable
FROM information_schema.columns 
WHERE table_name IN ('expense_details', 'rd_cost_details', 'rental_asset_details', 'operating_asset_details', 'fixed_asset_details')
ORDER BY table_name, ordinal_position;
