-- 回滚脚本：移除公司编码字段
-- 如果迁移出现问题，可以使用此脚本回滚

-- 1. 移除支出明细表的公司编码字段
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'expense_details' AND column_name = 'companyCode') THEN
        ALTER TABLE expense_details DROP COLUMN "companyCode";
    END IF;
END $$;

-- 2. 移除研发成本明细表的公司编码字段
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'rd_cost_details' AND column_name = 'companyCode') THEN
        ALTER TABLE rd_cost_details DROP COLUMN "companyCode";
    END IF;
END $$;

-- 3. 移除租赁资产明细表的公司编码字段
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'rental_asset_details' AND column_name = 'companyCode') THEN
        ALTER TABLE rental_asset_details DROP COLUMN "companyCode";
    END IF;
END $$;

-- 4. 移除运营资产明细表的公司编码字段
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'operating_asset_details' AND column_name = 'companyCode') THEN
        ALTER TABLE operating_asset_details DROP COLUMN "companyCode";
    END IF;
END $$;

-- 5. 移除固定资产明细表的公司编码字段
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'fixed_asset_details' AND column_name = 'companyCode') THEN
        ALTER TABLE fixed_asset_details DROP COLUMN "companyCode";
    END IF;
END $$;
