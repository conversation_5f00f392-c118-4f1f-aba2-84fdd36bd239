-- 为所有资产明细表添加公司编码字段
-- 执行时间：2024年

-- 1. 为支出明细表添加公司编码字段
ALTER TABLE expense_details 
ADD COLUMN "companyCode" VARCHAR(50) NULL COMMENT '公司编码（必填）';

-- 2. 为研发成本明细表添加公司编码字段
ALTER TABLE rd_cost_details 
ADD COLUMN "companyCode" VARCHAR(50) NULL COMMENT '公司编码（必填）';

-- 3. 为租赁资产明细表添加公司编码字段
ALTER TABLE rental_asset_details 
ADD COLUMN "companyCode" VARCHAR(50) NULL COMMENT '公司编码（必填）';

-- 4. 为运营资产明细表添加公司编码字段
ALTER TABLE operating_asset_details 
ADD COLUMN "companyCode" VARCHAR(50) NULL COMMENT '公司编码（必填）';

-- 5. 为固定资产明细表添加公司编码字段
ALTER TABLE fixed_asset_details 
ADD COLUMN "companyCode" VARCHAR(50) NULL COMMENT '公司编码（必填）';

-- 6. 将现有数据的公司编码设置为 '01'
UPDATE expense_details SET "companyCode" = '01' WHERE "companyCode" IS NULL;
UPDATE rd_cost_details SET "companyCode" = '01' WHERE "companyCode" IS NULL;
UPDATE rental_asset_details SET "companyCode" = '01' WHERE "companyCode" IS NULL;
UPDATE operating_asset_details SET "companyCode" = '01' WHERE "companyCode" IS NULL;
UPDATE fixed_asset_details SET "companyCode" = '01' WHERE "companyCode" IS NULL;

-- 7. 将字段设置为非空约束
ALTER TABLE expense_details 
ALTER COLUMN "companyCode" SET NOT NULL;

ALTER TABLE rd_cost_details 
ALTER COLUMN "companyCode" SET NOT NULL;

ALTER TABLE rental_asset_details 
ALTER COLUMN "companyCode" SET NOT NULL;

ALTER TABLE operating_asset_details 
ALTER COLUMN "companyCode" SET NOT NULL;

ALTER TABLE fixed_asset_details 
ALTER COLUMN "companyCode" SET NOT NULL;

-- 8. 添加外键约束（可选，如果需要强制引用完整性）
-- ALTER TABLE expense_details 
-- ADD CONSTRAINT fk_expense_details_company 
-- FOREIGN KEY ("companyCode") REFERENCES companies(code);

-- ALTER TABLE rd_cost_details 
-- ADD CONSTRAINT fk_rd_cost_details_company 
-- FOREIGN KEY ("companyCode") REFERENCES companies(code);

-- ALTER TABLE rental_asset_details 
-- ADD CONSTRAINT fk_rental_asset_details_company 
-- FOREIGN KEY ("companyCode") REFERENCES companies(code);

-- ALTER TABLE operating_asset_details 
-- ADD CONSTRAINT fk_operating_asset_details_company 
-- FOREIGN KEY ("companyCode") REFERENCES companies(code);

-- ALTER TABLE fixed_asset_details 
-- ADD CONSTRAINT fk_fixed_asset_details_company 
-- FOREIGN KEY ("companyCode") REFERENCES companies(code);
