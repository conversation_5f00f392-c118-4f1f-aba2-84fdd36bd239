import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ExpensesService } from './expenses.service';
import { ExpensesController } from './expenses.controller';
import { Expense } from './entities/expense.entity';
import { ExpenseDetail } from './entities/expense-detail.entity';
import { SuppliersModule } from '@/suppliers/suppliers.module';
import { CompaniesModule } from '@/companies/companies.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Expense, ExpenseDetail]),
    SuppliersModule,
    CompaniesModule,
  ],
  controllers: [ExpensesController],
  providers: [ExpensesService],
  exports: [ExpensesService],
})
export class ExpensesModule {}
