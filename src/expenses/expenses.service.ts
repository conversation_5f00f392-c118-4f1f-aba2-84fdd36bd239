import {
  Injectable,
  Logger,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import * as ExcelJS from 'exceljs';
import axios from 'axios';
import { Expense } from './entities/expense.entity';
import { ExpenseDetail } from './entities/expense-detail.entity';
import { CreateExpenseDetailDto } from './dto/create-expense-detail.dto';
import { UpdateExpenseDetailDto } from './dto/update-expense-detail.dto';
import { QueryExpenseDetailsDto } from './dto/query-expense-details.dto';
import { ProcessedExportExpenseDto } from './dto/export-expense-details.dto';
import { ExpenseImportResult } from './dto/expense-import-result.dto';
import { SuppliersService } from '../suppliers/suppliers.service';
import { CompaniesService } from '../companies/companies.service';

@Injectable()
export class ExpensesService {
  private readonly logger = new Logger(ExpensesService.name);

  constructor(
    @InjectRepository(Expense)
    private readonly expenseRepository: Repository<Expense>,
    @InjectRepository(ExpenseDetail)
    private readonly expenseDetailRepository: Repository<ExpenseDetail>,
    private readonly dataSource: DataSource,
    private readonly suppliersService: SuppliersService,
    private readonly companiesService: CompaniesService,
  ) {}

  // 获取或创建支出记录（系统只有一条记录）
  private async getOrCreateExpense(): Promise<Expense> {
    let expense = await this.expenseRepository.findOne({
      where: { isDeleted: false },
    });

    if (!expense) {
      expense = this.expenseRepository.create({
        totalAmount: 0,
        createDate: new Date().toISOString().split('T')[0], // 格式: YYYY-MM-DD
      });
      await this.expenseRepository.save(expense);
      this.logger.log('Created new expense record');
    }

    return expense;
  }

  // 验证供应商是否存在
  private async validateSupplier(
    supplierCode: string,
  ): Promise<{ code: string; name: string }> {
    if (!supplierCode || supplierCode.trim() === '') {
      throw new BadRequestException('供应商编码不能为空');
    }

    const supplier = await this.suppliersService.findByCode(supplierCode);
    if (!supplier) {
      throw new BadRequestException(`供应商 ${supplierCode} 不存在`);
    }

    return { code: supplier.code, name: supplier.name };
  }

  // 验证公司是否存在
  private async validateCompany(
    companyCode: string,
  ): Promise<{ code: string; name: string }> {
    if (!companyCode || companyCode.trim() === '') {
      throw new BadRequestException('公司编码不能为空');
    }

    const company = await this.companiesService.findByCode(companyCode);
    if (!company) {
      throw new BadRequestException(`公司 ${companyCode} 不存在`);
    }

    return { code: company.code, name: company.name };
  }

  // 新增支出明细
  async createDetail(createDetailDto: CreateExpenseDetailDto): Promise<void> {
    const {
      supplierCode,
      companyCode,
      amount,
      screenshot,
      remark,
      createDate,
    } = createDetailDto;

    this.logger.log(
      `Creating expense detail for supplier: ${supplierCode}, company: ${companyCode}, amount: ${amount}`,
    );

    // 验证供应商和公司
    const supplier = await this.validateSupplier(supplierCode);
    const company = await this.validateCompany(companyCode);

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // 获取或创建支出记录
      const expense = await this.getOrCreateExpense();

      // 创建明细记录
      const detail = queryRunner.manager.create(ExpenseDetail, {
        expenseId: expense.id,
        supplierCode: supplier.code,
        supplierName: supplier.name,
        companyCode: company.code,
        amount,
        screenshot: screenshot || null,
        remark: remark || null,
        createDate,
      });

      await queryRunner.manager.save(detail);

      // 更新总金额
      await queryRunner.manager.update(
        Expense,
        { id: expense.id },
        {
          totalAmount: () => `"totalAmount" + ${amount}`,
        },
      );

      await queryRunner.commitTransaction();
      this.logger.log(
        `Expense detail created successfully for supplier: ${supplierCode}, amount: ${amount}`,
      );
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  // 分页查询支出明细
  async findAllDetails(queryDto: QueryExpenseDetailsDto) {
    const {
      page,
      pageSize,
      startTime,
      endTime,
      supplierSearch,
      remarkSearch,
      sortBy = 'createDate',
      sortOrder = 'DESC',
    } = queryDto;

    this.logger.log(
      `Querying expense details: page=${page}, pageSize=${pageSize}`,
    );

    // 获取支出总金额
    const expense = await this.getOrCreateExpense();

    // 处理时间参数 - 由于createDate是字符串类型，需要特殊处理
    let processedStartTime = startTime;
    let processedEndTime = endTime;

    if (startTime) {
      if (startTime.length === 10) {
        // 对于日期字符串，我们需要确保包含开始日期的所有时间
        processedStartTime = startTime; // 保持原始日期格式用于字符串比较
      }
    }

    if (endTime) {
      if (endTime.length === 10) {
        // 对于结束日期，我们需要确保包含结束日期的所有时间
        processedEndTime = endTime; // 保持原始日期格式用于字符串比较
      }
    }

    const queryBuilder = this.expenseDetailRepository
      .createQueryBuilder('detail')
      .leftJoin('companies', 'company', 'company.code = detail.companyCode')
      .addSelect('company.name', 'companyName')
      .where('detail.isDeleted = false')
      .orderBy(`detail.${sortBy}`, sortOrder as 'ASC' | 'DESC');

    // 添加时间范围过滤 - 由于createDate是字符串类型，使用CAST转换为日期进行比较
    if (processedStartTime) {
      // 将字符串日期转换为日期类型进行比较，确保包含开始日期的所有数据
      queryBuilder.andWhere(
        'CAST(detail.createDate AS DATE) >= CAST(:startTime AS DATE)',
        {
          startTime: processedStartTime,
        },
      );
    }

    if (processedEndTime) {
      // 将字符串日期转换为日期类型进行比较，确保包含结束日期的所有数据
      queryBuilder.andWhere(
        'CAST(detail.createDate AS DATE) <= CAST(:endTime AS DATE)',
        {
          endTime: processedEndTime,
        },
      );
    }

    // 添加供应商搜索过滤
    if (supplierSearch) {
      queryBuilder.andWhere(
        '(detail.supplierCode ILIKE :supplierSearch OR detail.supplierName ILIKE :supplierSearch)',
        { supplierSearch: `%${supplierSearch}%` },
      );
    }

    // 添加备注搜索过滤
    if (remarkSearch) {
      queryBuilder.andWhere('detail.remark ILIKE :remarkSearch', {
        remarkSearch: `%${remarkSearch}%`,
      });
    }

    // 处理分页参数
    if (page === 0 && pageSize === 0) {
      // 返回所有数据
      const result = await queryBuilder.getRawAndEntities();
      const details = result.entities.map((detail, index) => ({
        ...detail,
        companyName: result.raw[index]?.companyName || null,
      }));

      // 计算查询结果的总金额
      const queryResultTotalAmount = details.reduce(
        (sum, detail) => sum + detail.amount,
        0,
      );

      return {
        details,
        total: details.length,
        page: 0,
        pageSize: 0,
        totalAmount: queryResultTotalAmount,
        systemTotalAmount: expense.totalAmount,
        startTime: processedStartTime || null,
        endTime: processedEndTime || null,
      };
    }

    // 正常分页查询
    const paginatedResult = await queryBuilder
      .skip((page - 1) * pageSize)
      .take(pageSize)
      .getRawAndEntities();

    const details = paginatedResult.entities.map((detail, index) => ({
      ...detail,
      companyName: paginatedResult.raw[index]?.companyName || null,
    }));

    // 获取总数
    const total = await queryBuilder.getCount();

    // 计算所有查询条件下的总金额（不分页）
    const allMatchingResult = await queryBuilder
      .skip(0)
      .take(undefined)
      .getRawAndEntities();

    const queryResultTotalAmount = allMatchingResult.entities.reduce(
      (sum, detail) => sum + detail.amount,
      0,
    );

    return {
      details,
      total,
      page,
      pageSize,
      totalAmount: queryResultTotalAmount,
      systemTotalAmount: expense.totalAmount,
      startTime: processedStartTime || null,
      endTime: processedEndTime || null,
    };
  }

  // 获取支出明细详情
  async findDetailById(id: string): Promise<ExpenseDetail> {
    this.logger.log(`Finding expense detail by id: ${id}`);

    const detail = await this.expenseDetailRepository.findOne({
      where: { id, isDeleted: false },
    });

    if (!detail) {
      throw new NotFoundException('支出明细不存在');
    }

    return detail;
  }

  // 更新支出明细
  async updateDetail(
    id: string,
    updateDetailDto: UpdateExpenseDetailDto,
  ): Promise<void> {
    this.logger.log(`Updating expense detail ${id}`);

    const detail = await this.findDetailById(id);
    const oldAmount = detail.amount;

    // 如果更新了供应商，需要验证
    if (
      updateDetailDto.supplierCode &&
      updateDetailDto.supplierCode !== detail.supplierCode
    ) {
      const supplier = await this.validateSupplier(
        updateDetailDto.supplierCode,
      );
      detail.supplierCode = supplier.code;
      detail.supplierName = supplier.name;
    }

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // 更新明细
      Object.assign(detail, updateDetailDto);
      await queryRunner.manager.save(detail);

      // 如果金额发生变化，更新总金额
      if (
        updateDetailDto.amount !== undefined &&
        updateDetailDto.amount !== oldAmount
      ) {
        const amountDiff = updateDetailDto.amount - oldAmount;
        const expense = await this.getOrCreateExpense();

        await queryRunner.manager.update(
          Expense,
          { id: expense.id },
          {
            totalAmount: () => `"totalAmount" + ${amountDiff}`,
          },
        );
      }

      await queryRunner.commitTransaction();
      this.logger.log(`Expense detail ${id} updated successfully`);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  // 删除支出明细
  async removeDetail(id: string): Promise<void> {
    this.logger.log(`Removing expense detail ${id}`);

    const detail = await this.findDetailById(id);

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // 软删除明细
      detail.isDeleted = true;
      await queryRunner.manager.save(detail);

      // 更新总金额
      const expense = await this.getOrCreateExpense();
      await queryRunner.manager.update(
        Expense,
        { id: expense.id },
        {
          totalAmount: () => `"totalAmount" - ${detail.amount}`,
        },
      );

      await queryRunner.commitTransaction();
      this.logger.log(`Expense detail ${id} removed successfully`);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  // 导出支出明细Excel
  async exportDetailsToExcel(
    exportDto: ProcessedExportExpenseDto,
  ): Promise<Buffer> {
    this.logger.log('Exporting expense details to Excel');

    // 验证：如果没有提供明细ID，必须提供时间范围
    if (!exportDto.detailIds || exportDto.detailIds.length === 0) {
      if (!exportDto.startTime || !exportDto.endTime) {
        throw new BadRequestException(
          '请选择明细进行导出，或提供起始时间和终止时间进行时间范围导出',
        );
      }
    }

    let queryBuilder = this.expenseDetailRepository
      .createQueryBuilder('detail')
      .leftJoin('companies', 'company', 'company.code = detail.companyCode')
      .where('detail.isDeleted = false')
      .orderBy('detail.createDate', 'DESC');

    // 明细ID过滤（可选）
    if (exportDto.detailIds && exportDto.detailIds.length > 0) {
      queryBuilder.andWhere('detail.id IN (:...detailIds)', {
        detailIds: exportDto.detailIds,
      });
    }

    // 时间范围过滤 - 由于createDate是字符串类型，使用CAST转换为日期进行比较
    if (exportDto.startTime) {
      const processedStartTime =
        exportDto.startTime.length === 10
          ? exportDto.startTime
          : exportDto.startTime;
      queryBuilder.andWhere(
        'CAST(detail.createDate AS DATE) >= CAST(:startTime AS DATE)',
        {
          startTime: processedStartTime,
        },
      );
    }

    if (exportDto.endTime) {
      const processedEndTime =
        exportDto.endTime.length === 10 ? exportDto.endTime : exportDto.endTime;
      queryBuilder.andWhere(
        'CAST(detail.createDate AS DATE) <= CAST(:endTime AS DATE)',
        {
          endTime: processedEndTime,
        },
      );
    }

    // 供应商搜索过滤
    if (exportDto.supplierSearch) {
      queryBuilder.andWhere(
        '(detail.supplierCode ILIKE :supplierSearch OR detail.supplierName ILIKE :supplierSearch)',
        { supplierSearch: `%${exportDto.supplierSearch}%` },
      );
    }

    // 公司编码过滤
    if (exportDto.companyCode) {
      queryBuilder.andWhere('detail.companyCode = :companyCode', {
        companyCode: exportDto.companyCode,
      });
    }

    const details = await queryBuilder.getMany();

    // 验证是否找到了明细
    if (details.length === 0) {
      throw new BadRequestException('未找到符合条件的支出明细');
    }

    // 计算选择性统计数据
    const totalAmount = details.reduce((sum, detail) => sum + detail.amount, 0);

    // 生成Excel（支持图片插入）
    try {
      // 创建工作簿
      const workbook = new ExcelJS.Workbook();
      const worksheet = workbook.addWorksheet('支出明细');

      // 设置列宽
      worksheet.columns = [
        { header: '序号', key: 'index', width: 8 },
        { header: '供应商编码', key: 'supplierCode', width: 15 },
        { header: '供应商名称', key: 'supplierName', width: 20 },
        { header: '支出金额', key: 'amount', width: 15 },
        { header: '截图', key: 'screenshot', width: 30 },
        { header: '备注', key: 'remark', width: 30 },
        { header: '创建日期', key: 'createDate', width: 20 },
      ];

      // 设置标题行样式
      const headerRow = worksheet.getRow(1);
      headerRow.font = { bold: true };
      headerRow.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFE0E0E0' },
      };

      let currentRow = 2; // 从第二行开始（第一行是标题）

      // 添加数据行并处理图片
      for (let i = 0; i < details.length; i++) {
        const detail = details[i];

        // 设置行高以容纳图片
        worksheet.getRow(currentRow).height = 80;

        const row = worksheet.addRow([
          i + 1,
          detail.supplierCode,
          detail.supplierName || '',
          detail.amount,
          '', // 截图列先留空，后面插入图片
          detail.remark || '',
          detail.createDate,
        ]);

        // 下载并插入图片
        try {
          if (detail.screenshot) {
            this.logger.log(`Downloading image: ${detail.screenshot}`);

            const imageResponse = await axios.get(detail.screenshot, {
              responseType: 'arraybuffer',
              timeout: 10000,
            });

            const imageBuffer = Buffer.from(imageResponse.data);

            // 添加图片到工作簿
            const imageId = workbook.addImage({
              buffer: imageBuffer,
              extension: 'jpeg',
            });

            // 在截图列插入图片
            worksheet.addImage(imageId, {
              tl: { col: 4, row: currentRow - 1 }, // 从截图列开始（第5列，索引4）
              ext: { width: 200, height: 100 }, // 图片大小
            });

            this.logger.log(`Image inserted successfully for detail ${i + 1}`);
          }
        } catch (imageError) {
          this.logger.warn(
            `Failed to load image for detail ${i + 1}: ${detail.screenshot}`,
            imageError.message,
          );
          // 如果图片加载失败，在截图列显示链接
          row.getCell(5).value = detail.screenshot;
        }

        currentRow++;
      }

      // 添加统计数据行
      worksheet.addRow([]); // 空行
      currentRow++;

      const summaryTitleRow = worksheet.addRow([
        '统计汇总（仅针对选中的明细）',
      ]);
      summaryTitleRow.font = { bold: true, size: 12 };
      summaryTitleRow.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFCCCCCC' },
      };
      currentRow++;

      const totalAmountRow = worksheet.addRow(['总支出金额', totalAmount]);
      totalAmountRow.font = { bold: true };
      totalAmountRow.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFF0F0F0' },
      };
      currentRow++;

      const countRow = worksheet.addRow(['明细数量', details.length]);
      countRow.font = { bold: true };
      countRow.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFF0F0F0' },
      };

      // 设置边框
      const borderStyle = {
        top: { style: 'thin' as const },
        left: { style: 'thin' as const },
        bottom: { style: 'thin' as const },
        right: { style: 'thin' as const },
      };

      // 为表格添加边框
      const tableStartRow = 1;
      const tableEndRow = details.length + 1;
      for (let row = tableStartRow; row <= tableEndRow; row++) {
        for (let col = 1; col <= 7; col++) {
          worksheet.getCell(row, col).border = borderStyle;
        }
      }

      // 生成Excel缓冲区
      const excelBuffer = await workbook.xlsx.writeBuffer();
      return Buffer.from(excelBuffer);
    } catch (error) {
      this.logger.error('Excel generation failed', error);
      throw new BadRequestException('Excel生成失败');
    }
  }

  /**
   * 生成支出明细导入模板Excel
   */
  async generateImportTemplate(): Promise<Buffer> {
    this.logger.log('Generating expense details import template');

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('支出明细导入模板');

    // 设置列标题
    const headers = [
      '供应商编码（必填）',
      '支出金额（必填）',
      '截图（可选）',
      '备注（可选）',
      '创建时间（必填，格式：2025/1/1）',
    ];

    worksheet.addRow(headers);

    // 设置标题行样式
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFE0E0E0' },
    };

    // 设置列宽
    worksheet.columns = [
      { width: 20 }, // 供应商编码
      { width: 15 }, // 支出金额
      { width: 40 }, // 截图
      { width: 30 }, // 备注
      { width: 25 }, // 创建时间
    ];

    // 添加示例数据
    const exampleData = [
      [
        'SUP001',
        15000.5,
        'https://example.com/screenshot1.jpg',
        '采购办公用品',
        '2025/1/15',
      ],
      ['SUP002', 8500.0, '', '设备维护费用', '2025/1/16'],
    ];

    exampleData.forEach((row) => {
      worksheet.addRow(row);
    });

    // 设置示例数据样式
    for (let i = 2; i <= 3; i++) {
      const row = worksheet.getRow(i);
      row.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFF8F8F8' },
      };
    }

    // 生成Excel缓冲区
    const excelBuffer = await workbook.xlsx.writeBuffer();
    return Buffer.from(excelBuffer);
  }

  /**
   * 解析和验证日期格式（支持 YYYY/M/D 格式和Excel Date对象）
   */
  private parseAndValidateDate(
    dateValue: any,
    rowNumber: number,
  ): string | null {
    try {
      // 添加调试日志
      this.logger.debug(
        `Row ${rowNumber}: Processing date value: ${dateValue}, type: ${typeof dateValue}`,
      );

      let year: number, month: number, day: number;

      // 处理Date对象（Excel可能会将日期转换为Date对象）
      if (dateValue instanceof Date) {
        year = dateValue.getFullYear();
        month = dateValue.getMonth() + 1; // getMonth() 返回 0-11
        day = dateValue.getDate();
      } else {
        // 处理字符串格式
        const dateStr = dateValue.toString().trim();

        // 如果是Date对象的toString()结果，尝试解析
        if (dateStr.includes('GMT') || dateStr.includes('UTC')) {
          const parsedDate = new Date(dateStr);
          if (!isNaN(parsedDate.getTime())) {
            year = parsedDate.getFullYear();
            month = parsedDate.getMonth() + 1;
            day = parsedDate.getDate();
          } else {
            return null;
          }
        } else {
          // 支持的格式：YYYY/M/D, YYYY/MM/DD
          const dateRegex = /^(\d{4})\/(\d{1,2})\/(\d{1,2})$/;
          const match = dateStr.match(dateRegex);

          if (!match) {
            return null;
          }

          year = parseInt(match[1]);
          month = parseInt(match[2]);
          day = parseInt(match[3]);
        }
      }

      // 验证日期有效性
      if (year < 1900 || year > 2100) {
        return null;
      }

      if (month < 1 || month > 12) {
        return null;
      }

      if (day < 1 || day > 31) {
        return null;
      }

      // 创建Date对象验证日期是否真实存在
      const date = new Date(year, month - 1, day);
      if (
        date.getFullYear() !== year ||
        date.getMonth() !== month - 1 ||
        date.getDate() !== day
      ) {
        return null;
      }

      // 返回标准格式的日期字符串 (YYYY-MM-DD)
      const formattedMonth = month.toString().padStart(2, '0');
      const formattedDay = day.toString().padStart(2, '0');
      return `${year}-${formattedMonth}-${formattedDay}`;
    } catch (error) {
      this.logger.warn(
        `Date parsing failed for row ${rowNumber}: ${error.message}`,
      );
      return null;
    }
  }

  /**
   * 从Excel导入支出明细数据
   */
  async importFromExcel(
    file: Express.Multer.File,
  ): Promise<ExpenseImportResult> {
    this.logger.log(
      `Importing expense details from Excel: ${file.originalname}`,
    );

    const result: ExpenseImportResult = {
      successCount: 0,
      failureCount: 0,
      errors: [],
      successSupplierCodes: [],
    };

    try {
      // 读取Excel文件
      const workbook = new ExcelJS.Workbook();
      await workbook.xlsx.load(file.buffer);
      const worksheet = workbook.getWorksheet(1);

      if (!worksheet) {
        throw new BadRequestException('Excel文件格式错误');
      }

      // 跳过标题行，从第二行开始处理
      for (let rowNumber = 2; rowNumber <= worksheet.rowCount; rowNumber++) {
        const row = worksheet.getRow(rowNumber);

        // 跳过空行
        if (!row.getCell(1).value && !row.getCell(2).value) {
          continue;
        }

        try {
          // 获取单元格数据
          const supplierCode = row.getCell(1).value?.toString().trim();
          const amountValue = row.getCell(2).value;
          const screenshot =
            row.getCell(3).value?.toString().trim() || undefined;
          const remark = row.getCell(4).value?.toString().trim() || undefined;
          const createDateValue = row.getCell(5).value; // 不要立即转换为字符串，保持原始类型

          // 验证必填字段
          if (!supplierCode) {
            result.errors.push(`第${rowNumber}行：供应商编码不能为空`);
            result.failureCount++;
            continue;
          }

          if (
            !amountValue ||
            isNaN(Number(amountValue)) ||
            Number(amountValue) <= 0
          ) {
            result.errors.push(`第${rowNumber}行：支出金额必须是大于0的数字`);
            result.failureCount++;
            continue;
          }

          if (!createDateValue) {
            result.errors.push(`第${rowNumber}行：创建时间不能为空`);
            result.failureCount++;
            continue;
          }

          // 解析和验证日期格式
          const createDate = this.parseAndValidateDate(
            createDateValue,
            rowNumber,
          );
          if (!createDate) {
            result.errors.push(
              `第${rowNumber}行：创建时间格式错误，请使用 YYYY/M/D 格式，如：2025/1/1`,
            );
            result.failureCount++;
            continue;
          }

          const amount = Number(amountValue);

          // 创建支出明细DTO（导入时默认使用公司编码'01'）
          const createDetailDto: CreateExpenseDetailDto = {
            supplierCode,
            companyCode: '01', // 导入时默认使用公司编码'01'
            amount,
            screenshot,
            remark,
            createDate,
          };

          // 调用现有的创建方法
          await this.createDetail(createDetailDto);

          result.successCount++;
          if (!result.successSupplierCodes.includes(supplierCode)) {
            result.successSupplierCodes.push(supplierCode);
          }
        } catch (error) {
          result.failureCount++;
          const errorMessage =
            error instanceof Error ? error.message : '未知错误';
          result.errors.push(`第${rowNumber}行：${errorMessage}`);
          this.logger.warn(
            `Import failed for row ${rowNumber}: ${errorMessage}`,
          );
        }
      }

      this.logger.log(
        `Import completed: ${result.successCount} success, ${result.failureCount} failed`,
      );
      return result;
    } catch (error) {
      this.logger.error('Excel import failed', error);
      throw new BadRequestException(`Excel导入失败: ${error.message}`);
    }
  }
}
