import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  Join<PERSON><PERSON>umn,
} from 'typeorm';
import { RentalAsset } from './rental-asset.entity';

// 租赁资产类型枚举
export enum RentalAssetType {
  SHORT_TERM = 'short_term', // 短期租赁
  LONG_TERM = 'long_term', // 长期租赁
}

@Entity('rental_asset_details')
export class RentalAssetDetail {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'uuid', comment: '租赁资产ID' })
  rentalAssetId: string;

  @Column({
    type: 'enum',
    enum: RentalAssetType,
    comment: '租赁资产类型',
  })
  type: RentalAssetType;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    comment: '明细金额（保留两位小数）',
    transformer: {
      to: (value: number) => value,
      from: (value: string) => parseFloat(value),
    },
  })
  amount: number;

  @Column({
    type: 'varchar',
    length: 500,
    comment: '截图URL（必填）',
  })
  screenshot: string;

  @Column({
    type: 'text',
    nullable: true,
    comment: '备注（可选）',
  })
  remark: string | null;

  @Column({
    type: 'boolean',
    default: false,
    comment: '是否已删除',
  })
  isDeleted: boolean;

  @Column({
    type: 'varchar',
    length: 20,
    nullable: true,
    comment: '创建日期（字符串格式，必填）',
    name: 'createdAt',
  })
  createDate: string;

  @Column({
    type: 'varchar',
    length: 50,
    comment: '公司编码（必填）',
  })
  companyCode: string;

  // 关联租赁资产
  @ManyToOne(() => RentalAsset, (rentalAsset) => rentalAsset.details)
  @JoinColumn({ name: 'rentalAssetId' })
  rentalAsset: RentalAsset;
}
