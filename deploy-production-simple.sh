#!/bin/bash

# ========================================
# 简化版生产环境部署脚本
# ========================================
# 用法: ./deploy-production-simple.sh

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 数据库连接信息（服务器本地，自动填入密码）
DB_HOST="localhost"
DB_USER="postgres"
DB_NAME="manager"
DB_PASSWORD="54188"

# 设置PGPASSWORD环境变量，避免重复输入密码
export PGPASSWORD=$DB_PASSWORD

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 安装依赖
install_dependencies() {
    log_info "安装项目依赖..."
    pnpm install --frozen-lockfile
    log_success "依赖安装完成"
}

# 构建项目
build_project() {
    log_info "构建生产版本..."

    # 复制生产环境配置
    cp .env.production .env
    log_info "已复制生产环境配置"

    # 构建项目
    pnpm run build
    log_success "项目构建完成"
}

# 备份数据库（简化版）
backup_database() {
    log_info "备份数据库..."

    local backup_dir="./backups"
    local backup_file="$backup_dir/manager_backup_$(date +%Y%m%d_%H%M%S).sql"

    # 创建备份目录
    mkdir -p "$backup_dir"

    # 执行备份（使用远程数据库）
    if pg_dump -h $DB_HOST -U $DB_USER -d $DB_NAME > "$backup_file" 2>/dev/null; then
        log_success "数据库备份完成: $backup_file"
        # 保留最近3个备份文件
        ls -t "$backup_dir"/manager_backup_*.sql 2>/dev/null | tail -n +4 | xargs rm -f 2>/dev/null || true
    else
        log_warning "数据库备份失败，但继续执行部署"
    fi
}

# 简化的数据库迁移（跳过复杂检查）
run_migrations() {
    log_info "执行数据库迁移..."

    # 测试数据库连接
    log_info "测试数据库连接..."
    if ! psql -h $DB_HOST -U $DB_USER -d $DB_NAME -c "SELECT 1;" >/dev/null 2>&1; then
        log_error "数据库连接失败，请检查数据库服务状态"
        exit 1
    fi
    log_success "数据库连接正常"

    # 确保 uuid-ossp 扩展已启用
    log_info "确保 uuid-ossp 扩展已启用..."
    psql -h $DB_HOST -U $DB_USER -d $DB_NAME -c "CREATE EXTENSION IF NOT EXISTS \"uuid-ossp\";" >/dev/null 2>&1 || true

    # 执行迁移（简化版，不做复杂检查）
    log_info "执行数据库迁移..."
    if pnpm run migration:run; then
        log_success "数据库迁移完成"
    else
        log_warning "数据库迁移失败，但继续部署（可能是迁移已执行过）"
    fi
}

# 停止现有服务
stop_service() {
    log_info "停止现有服务..."
    
    # 停止PM2进程
    pm2 stop web-manager-backend 2>/dev/null || log_warning "PM2进程未运行"
    pm2 delete web-manager-backend 2>/dev/null || log_warning "PM2进程不存在"
    
    log_success "服务停止完成"
}

# 启动服务
start_service() {
    log_info "启动生产服务..."
    
    # 使用PM2启动服务
    pm2 start ecosystem.config.js --env production
    
    # 保存PM2配置
    pm2 save
    
    log_success "服务启动完成"
}

# 创建PM2配置文件
create_pm2_config() {
    log_info "创建PM2配置文件..."
    
    cat > ecosystem.config.js << EOF
module.exports = {
  apps: [{
    name: 'web-manager-backend',
    script: 'start-production.js',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'development'
    },
    env_production: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true
  }]
};
EOF
    
    # 创建日志目录
    mkdir -p logs
    
    log_success "PM2配置文件创建完成"
}

# 简化的服务检查
check_service() {
    log_info "检查服务状态..."
    
    sleep 3
    
    # 检查PM2状态
    pm2 status
    
    # 检查端口是否监听
    if netstat -tuln | grep -q ":3000 "; then
        log_success "服务正在端口3000上运行"
    else
        log_warning "服务可能未在端口3000上运行，请检查日志"
        pm2 logs web-manager-backend --lines 10
    fi
    
    # 简单的API测试
    log_info "测试API连接..."
    if curl -f http://localhost:3000/api >/dev/null 2>&1; then
        log_success "API测试成功"
    else
        log_warning "API测试失败，请检查服务状态"
    fi
}

# 显示部署信息
show_deployment_info() {
    log_success "部署完成！"
    echo
    echo "=========================================="
    echo "🎉 部署信息"
    echo "=========================================="
    echo "应用名称: web-manager-backend"
    echo "运行环境: production"
    echo "监听端口: 3000"
    echo "数据库: $DB_HOST/$DB_NAME"
    echo "API文档: http://*************:3000/api"
    echo "=========================================="
    echo
    echo "常用命令:"
    echo "查看日志: pm2 logs web-manager-backend"
    echo "重启服务: pm2 restart web-manager-backend"
    echo "停止服务: pm2 stop web-manager-backend"
    echo "查看状态: pm2 status"
    echo "=========================================="
}

# 主函数
main() {
    log_info "开始简化部署 web-manager-backend"
    
    install_dependencies
    build_project
    backup_database
    run_migrations
    stop_service
    create_pm2_config
    start_service
    check_service
    show_deployment_info
}

# 脚本入口
if [ "${BASH_SOURCE[0]}" = "${0}" ]; then
    main "$@"
fi
