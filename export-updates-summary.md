# 导出功能公司编码更新总结

## 已完成的导出更新

### ✅ expenses 模块
- 更新了 `ExportExpenseDetailsDto` 和 `ProcessedExportExpenseDto`，添加 `companyCode` 字段
- 更新了 `exportDetailsToExcel` 方法，添加公司表JOIN和筛选条件
- 更新了控制器的API文档和参数处理

### ✅ rd-costs 模块
- 更新了 `ProcessedExportDto` 接口，添加 `companyCode` 字段
- 更新了 `exportToExcel` 方法，添加公司表JOIN和筛选条件

## 需要完成的导出更新

### 🔄 operating-assets 模块
需要更新：
1. 导出方法的查询构建器，添加公司表JOIN
2. 添加公司编码筛选条件
3. 控制器的API文档和参数处理

### 🔄 rental-assets 模块
需要检查是否有导出功能，如果有则需要更新：
1. ProcessedExportDto接口
2. 导出方法的查询构建器
3. 控制器的API文档和参数处理

### 🔄 fixed-assets 模块
需要检查是否有导出功能，如果有则需要更新：
1. ProcessedExportDto接口
2. 导出方法的查询构建器
3. 控制器的API文档和参数处理

## 通用更新模式

### 1. DTO/接口更新
```typescript
// 导出DTO
@ApiProperty({
  description: '公司编码筛选',
  example: '01',
  required: false,
})
@IsOptional()
@IsString()
companyCode?: string;

// 内部处理接口
interface ProcessedExportDto {
  // ... 其他字段
  companyCode?: string;
}
```

### 2. 服务方法更新
```typescript
// 查询构建器添加JOIN
const queryBuilder = this.detailRepository
  .createQueryBuilder('detail')
  .leftJoin('companies', 'company', 'company.code = detail.companyCode')
  .where('detail.isDeleted = false')
  .orderBy('detail.createDate', 'DESC');

// 添加筛选条件
if (exportDto.companyCode) {
  queryBuilder.andWhere('detail.companyCode = :companyCode', {
    companyCode: exportDto.companyCode,
  });
}
```

### 3. 控制器更新
```typescript
// API文档
@ApiQuery({
  name: 'companyCode',
  description: '公司编码筛选',
  example: '01',
  required: false,
})

// 参数提取
const { startTime, endTime, detailIds, companyCode } = req.query;

// 参数处理
if (companyCode) {
  exportParams.companyCode = decodeURIComponent(companyCode);
}
```

## 导出功能的业务价值

添加公司编码筛选到导出功能后，用户可以：

1. **按公司导出** - 只导出特定公司的资产明细
2. **多公司管理** - 在多公司环境下，各公司可以独立导出自己的数据
3. **数据隔离** - 确保公司间的数据安全和隔离
4. **报表分析** - 便于按公司维度进行财务分析和报表生成

## API使用示例

```bash
# 导出特定公司的支出明细
GET /expenses/export?startTime=2024-01-01&endTime=2024-12-31&companyCode=01

# 导出特定公司的研发成本
GET /rd-costs/export?startTime=2024-01-01&endTime=2024-12-31&companyCode=01

# 导出特定公司的运营资产
GET /operating-assets/export?startTime=2024-01-01&endTime=2024-12-31&companyCode=01
```

## 测试建议

1. 测试不传companyCode参数，应该返回所有公司的数据
2. 测试传入有效的companyCode，应该只返回该公司的数据
3. 测试传入无效的companyCode，应该返回空结果
4. 测试与其他筛选条件的组合使用
5. 验证导出的Excel文件内容正确性
